using System;
using System.Collections.Generic;
using System.Linq;
using RediSoftware.Dtos;
using RediSoftware.Redi_Utility;
using System.Web;
using System.Configuration;


using RediSoftware.Helpers;
using static RediSoftware.BusinessLogic.ComplianceMethod;
using System.Web.Hosting;
using RediSoftware.Models;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.ServiceModel.Configuration;
using NetTopologySuite.Operation.Overlay;
using Newtonsoft.Json.Linq;
using RediSoftware.BusHelper;

namespace RediSoftware.BusinessLogic
{

    // TODO: Get this working with the new specification summary report gen, THEN do the below (remove functions etc)
    //  - Important to make sure it works before we delete stuff otherwise it could be verrryyyy difficult to figure out
    //    what broke.
    // TODO: Remove all old no longer used functions
    // TODO: Split into a base class which data shared across different reports and sub-classes for specific reports
    // (e.g. pbdb, specification summary, etc).
    // Thinking: ReportData
    // ComplianceReportData
    // SpecificationSummaryReportData
    // LightAndVentilationReportData
    public class PdfReportNVelocityData
    {
        private Guid _jobId;
        private Guid _assessmentId;

        private ExtendedAssessmentDto _assessmentDto;
        private JobDto _jobDto = null;
        private ClientDto _clientDto = null;

        private UserDto _lastAssignedAssessor;

        private IUnitOfWork _unitOfWork;
        private Func<Job> _jobFactory;
        private Func<Assessment> _assessmentFactory;
        private Func<Client> _clientFactory;
        private Func<Construction> _constructionFactory;
        private Func<ServiceTemplate> _serviceFactory;

        private const string PD_OTHER_CODE = "PDOther";

        public StreetAddress StreetAddress { get; set; }

        public AssessmentComplianceOptionDto SelectedSimulation => _assessmentDto.SelectedSimulation;
        public AssessmentProjectDetailDto AssessmentProjectDetail => _assessmentDto.AssessmentProjectDetail;

        public PdfReportNVelocityData(IUnitOfWork unitOfWork,
            Func<Job> jobFactory,
            Func<Assessment> assessmentFactory,
            Func<AssessmentDrawing> assessmentDrawingFactory,
            Func<Client> clientFactory,
            Func<Construction> constructionFactory,
            Func<ServiceTemplate> serviceFactory)
        {
            _unitOfWork = unitOfWork;
            _jobFactory = jobFactory;
            _assessmentFactory = assessmentFactory;
            _clientFactory = clientFactory;
            _constructionFactory = constructionFactory;
            _serviceFactory = serviceFactory;



        }

        // TODO: This can be abstract on the base class...
        public PdfReportNVelocityData Initialize(Guid assessmentId)
        {
            _assessmentId = assessmentId;
            Assessment assessment = _assessmentFactory().Get(_assessmentId);

            _assessmentDto = assessment.ExtendedDto;

            if (_assessmentDto.JobId.HasValue)
            {
                _jobId = _assessmentDto.JobId.Value;
                _jobDto = _jobFactory().Get(_jobId).Dto;
                IsValid = true;

                if (_assessmentDto.AssessorUserId == null)
                {
                    _lastAssignedAssessor = assessment.GetLastAssignedAssessor();
                }

                _clientDto = _clientFactory().Get(_jobDto.ClientId).Dto;
                if (_clientDto.ClientDefault == null) {
                    _clientDto.ClientDefault = new ClientDefaultDto();
                }
            }
            else
            {
                Redi_Utility.ErrorWriter.WriteInfo(string.Format("Could not find job for assessment {0}", _assessmentId));
                IsValid = false;
            }

            if(IsValid)
            {
                PopulateAll();
            }

            return this;
        }





        public ExtendedAssessmentDto Assessment => _assessmentDto;

        public bool ClientOptions_IncludeDrawingsInReport
        {
            get
            {
                return _clientDto.ClientOptions.IncludeDrawingsInReport;
            }
        }

        public bool ClientOptions_StampDrawings
        {
            get
            {
                return _clientDto.ClientOptions.StampDrawings;
            }
        }



        public string DocumentVersion
        {
            get { return _assessmentDto.AssessmentProjectDetail?.AssessmentVersion.ToString("0.0"); }
        }



        public string Certification
        {
            get
            {
                return _assessmentDto.SelectedSimulation.Certification?.Title;
            }
        }

        public string State
        {
            get
            {
                if (_assessmentDto.AssessmentProjectDetail?.StateCode != null)
                {
                    return _assessmentDto.AssessmentProjectDetail?.StateCode;
                }
                else
                {
                    return string.Empty;
                }
            }
        }

        public bool IsValid { get; private set; } = false;

        public Guid JobId
        {
            get
            {
                if (_jobId != Guid.Empty)
                {
                    return _jobId;
                }
                else
                {
                    return Guid.Empty;
                }
            }
        }

        public Guid AssessmentId
        {
            get
            {
                if (_assessmentId != Guid.Empty)
                {
                    return _assessmentId;
                }
                else
                {
                    return Guid.Empty;
                }
            }
        }

        public Guid? EmployeeId
        {
            get
            {
                if (_assessmentDto != null && _assessmentDto.AssessorUserId.HasValue)
                {
                    return _assessmentDto.AssessorUserId.Value;
                }
                else
                {
                    if (_lastAssignedAssessor != null)
                    {
                        return _lastAssignedAssessor.UserId;
                    }
                    return null;
                }
            }
        }

        public string ProjectDescription
        {
            get
            {
                if (_assessmentDto.SelectedSimulation.Proposed.ProjectDescription == null)
                    return string.Empty;

                if (_assessmentDto.SelectedSimulation.Proposed.ProjectDescription.ProjectDescriptionCode == PD_OTHER_CODE)
                    return _assessmentDto.SelectedSimulation.Proposed.ProjectDescriptionOther;

                else
                    return _assessmentDto.SelectedSimulation.Proposed.ProjectDescription.Description;

            }
        }

        public string ProjectClassification
        {
            get
            {
                return _assessmentDto.SelectedSimulation.Proposed.ProjectClassification;
            }
        }

        public string ProjectAddress
        {
            get
            {
                return StreetAddress.FullAddress(true).Trim();
            }
        }

        public string ProjectAddressSingleLine
        {
            get
            {
                return StreetAddress.FullAddress(false).Trim();
            }
        }

        public bool UseCustomAddress
        {
            get
            {
                return _assessmentDto.AssessmentProjectDetail.UseCustomAddress.HasValue ? _assessmentDto.AssessmentProjectDetail.UseCustomAddress.Value : false;
            }
        }

        public string CustomDisplayAddress
        {
            get
            {
                return _assessmentDto.AssessmentProjectDetail.CustomDisplayAddress ?? String.Empty;
            }
        }

        public string CustomDisplayAddressMultiLine
        {
            get
            {
                string[] splitAddress = CustomDisplayAddress.Split('\n');
                var result = string.Empty;
                for (int ii = 0; ii < splitAddress.Length; ii++)
                {
                    if (ii != 0)
                    {
                        result += "<br/>";
                    }
                    result += splitAddress[ii];
                }
                result = result.Replace(",", "<br/>");
                return result;
            }
        }

        public string FinalAddressSingleLine
        {
            get
            {
                if (UseCustomAddress)
                    return CustomDisplayAddressSingleLine;
                else
                    return ProjectAddressSingleLine;
            }
        }

        public string FinalAddressMultiLine
        {
            get
            {
                if (UseCustomAddress)
                    return CustomDisplayAddressMultiLine;
                else
                    return ProjectAddress;
            }
        }

        public string CustomDisplayAddressSingleLine
        {
            get
            {
                return CustomDisplayAddress.Replace("\n", ", ").Trim();
            }
        }

        public string Owner
        {
            get
            {
                if (_assessmentDto.AssessmentProjectDetail?.ProjectOwner == null)
                {
                    return string.Empty;
                }
                return _assessmentDto.AssessmentProjectDetail?.ProjectOwner;
            }
        }

        public string NominatedBuildingSurveyorName
        {
            get
            {
                return _unitOfWork.Context.RSS_BuildingSurveyor.Find(_assessmentDto.AssessmentProjectDetail.NominatedBuildingSurveyorId).Description;
            }
        }

        public string LocalGovernmentAuthority
        {
            get
            {
                if (_assessmentDto.AssessmentProjectDetail?.LocalGovernmentAuthority == null)
                {
                    return string.Empty;
                }
                return _assessmentDto.AssessmentProjectDetail?.LocalGovernmentAuthority;
            }
        }

        public string JobReference
        {
            get
            {
                if (_jobDto.JobReference == null)
                {
                    return string.Empty;
                }
                return _jobDto.JobReference;
            }
        }

        public string ClientName
        {
            get
            {
                if (UseCustomClientName && !string.IsNullOrEmpty(_assessmentDto.AssessmentProjectDetail.CustomClientName))
                    return _assessmentDto.AssessmentProjectDetail.CustomClientName;
                else
                    return OriginalClientName;
            }
        }

        public string OriginalClientName
        {
            get
            {
                if (_jobDto.ClientName == null)
                {
                    return string.Empty;
                }
                return _jobDto.ClientName;
            }
        }

        public bool UseCustomClientName
        {
            get
            {
                return _assessmentDto.AssessmentProjectDetail.UseCustomClientName.HasValue ? _assessmentDto.AssessmentProjectDetail.UseCustomClientName.Value : false;
            }
        }

        public string CustomClientName
        {
            get
            {
                return _assessmentDto.AssessmentProjectDetail.CustomClientName ?? String.Empty;
            }
        }

        public string JobNumber
        {
            get
            {
                if (_assessmentDto.AssessmentProjectDetail?.ClientJobNumber == null)
                {
                    return string.Empty;
                }
                return _assessmentDto.AssessmentProjectDetail?.ClientJobNumber;
            }
        }

        public string NCCClimateZone
        {
            // Should be a number between 1 to 8 (which is NCCClimateZoneDescription field)
            get
            {
                if (_assessmentDto.NCCClimateZoneDescription == null)
                {
                    return string.Empty;
                }
                return _assessmentDto.NCCClimateZoneDescription;
            }
        }

        public string NCCClimateZoneDescription
        {
            // Should be a line of text (unique for each of the 8 NCC Climate Zones, ie. NCC5, I think)
            get
            {
                if (_assessmentDto.NCCClimateZoneDescription == null)
                {
                    return string.Empty;
                }

                // TODO: This should really be in the DB so there can be no drift from front and back end.
                if (_assessmentDto.NCCClimateZoneCode == "NCC1")
                    return "High humidity summer, warm winter";
                if (_assessmentDto.NCCClimateZoneCode == "NCC2")
                    return "Warm humid summer, mild winter";
                if (_assessmentDto.NCCClimateZoneCode == "NCC3")
                    return "Hot dry summer, warm winter";
                if (_assessmentDto.NCCClimateZoneCode == "NCC4")
                    return "Hot dry summer, cool winter";
                if (_assessmentDto.NCCClimateZoneCode == "NCC5")
                    return "Warm temperate";
                if (_assessmentDto.NCCClimateZoneCode == "NCC6")
                    return "Mild temperate";
                if (_assessmentDto.NCCClimateZoneCode == "NCC7")
                    return "Cool temperate";
                if (_assessmentDto.NCCClimateZoneCode == "NCC8")
                    return "Alpine";

                return "";
            }
        }
        public string VURBAcceptenceCriteria
        {
            get
            {
                string criteriaDescription;
                switch (_assessmentDto.NCCClimateZoneDescription)
                {
                    case "1":
                        criteriaDescription = "a cooling load equal to or less than that of the reference building; and";
                        break;
                    case "2":
                    case "3":
                    case "4":
                    case "5":
                    case "6":
                    case "7":
                        criteriaDescription = "a heating load and a cooling load equal to or less than that of the reference building; and";
                        break;
                    case "8":
                        criteriaDescription = "a heating load equal to or less than that of the reference building; and";
                        break;
                    default:
                        criteriaDescription = string.Empty;
                        break;
                }
                return criteriaDescription;
            }
        }

        public string NatHERSClimateZone
        {
            get
            {
                if (_assessmentDto.NatHERSClimateZoneDescription == null)
                {
                    return string.Empty;
                }
                return _assessmentDto.NatHERSClimateZoneDescription;
            }
        }

        public string ComplianceMethod
        {
            get
            {
                if (_assessmentDto.SelectedSimulation.ComplianceMethodCode == null)
                {
                    return string.Empty;
                }
                return _unitOfWork.Context.RSS_ComplianceMethod.FirstOrDefault(x => x.ComplianceMethodCode == _assessmentDto.SelectedSimulation.ComplianceMethodCode).Description;
            }
        }

        public int AllowedSHGCVariance => FinalComplianceMethodCode == "CMElementalProv" || FinalComplianceMethodCode == "CMPerfWAProtocolEP" ? 10 : 5;

        public EPComplianceData ComplianceMethodFloorData
        {
            get
            {
                if (_assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMElementalProv.ToString("G") ||
                    _assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfWAProtocolEP.ToString("G"))
                {
                    return _assessmentDto.SelectedSimulation.EPComplianceData;
                }
                return null;
            }
        }

        public string FinalComplianceMethodCode
        {
            get
            {
                return _assessmentDto.SelectedSimulation.ComplianceMethodCode;
            }
        }

        public bool ShowHFloorAreas
        {
            get
            {
                if (_assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMElementalProv.ToString("G") ||
                    _assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfWAProtocolEP.ToString("G"))
                {
                    return false;
                }

                return true;
            }
        }

        public bool ShowPerformanceLoads
        {
            get
            {
                if (_assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMElementalProv.ToString("G") ||
                    _assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfWAProtocolEP.ToString("G"))
                {
                    return false;
                }

                return true;
            }
        }

        public string AnnualPerformanceLoadsReferenceHeading
        {
            get
            {
                if (_assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfSolution.ToString("G"))
                    return "Reference";
                else if (_assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfSolutionDTS.ToString("G"))
                    return "DTS Provisions";
                else
                    return "";
            }
        }

        public bool IsPerformanceSolution
        {
            get
            {
                if (_assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfSolution.ToString("G") ||
                    _assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfSolutionDTS.ToString("G"))
                {
                    return true;
                }

                return false;
            }
        }

        public bool IsElementalProvision
        {
            get
            {
                if (_assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMElementalProv.ToString("G") ||
                    _assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfWAProtocolEP.ToString("G"))
                {
                    return true;
                }

                return false;
            }
        }

        public string IsElementalProvisionStr => IsElementalProvision ? "true" : "false";

        public bool IsComplianceMethod(string code) => _assessmentDto.SelectedSimulation.ComplianceMethodCode == code;

        public bool IsHouseEnergyRating
        {
            get
            {
                if (_assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMHouseEnergyRating.ToString("G") ||
                    _assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfSolutionHER.ToString("G") ||
                    _assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfWAProtocolHER.ToString("G") ||
                    _assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfELL.ToString("G"))
                {
                    return true;
                }

                return false;
            }
        }
        public bool IsComparisonDtsEp
        {
            get
            {
                if (_assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfSolutionDTS.ToString("G"))
                {
                    return true;
                }

                return false;
            }
        }
        public bool IsComparisonDtsHer
        {
            get
            {
                if (_assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfSolutionHER.ToString("G"))
                {
                    return true;
                }

                return false;
            }
        }
        public bool IsVurb
        {
            get
            {
                if (_assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfSolution.ToString("G"))
                {
                    return true;
                }

                return false;
            }
        }

        public string AnnualEnergyLoadsProposedHeader
        {
            get
            {
                // ...
                // if (_assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMHouseEnergyRating.ToString("G") ||
                //     _assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfSolutionHER.ToString("G") ||
                //     _assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfWAProtocolHER.ToString("G"))
                // {
                //     return "Calculated";
                // }


                return "Proposed Building";
            }
        }

        public string AnnualEnergyLoadsReferenceHeader
        {
            get
            {
                if (_assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMHouseEnergyRating.ToString("G") ||
                    _assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfSolutionHER.ToString("G") ||
                    _assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfWAProtocolHER.ToString("G"))
                {
                    return "Load Limit"; // ...
                }
                else if (IsComplianceMethod("CMPerfELL"))
                    return "Load Limit";
                else if (IsComplianceMethod("CMPerfSolutionDTS"))
                    return "DTS Building";
                else
                    return "Reference Building";

            }
        }

        public bool IsNcc2022Certification()
        {
            if (_assessmentDto.AssessmentProjectDetail == null ||
                _assessmentDto.SelectedSimulation.Certification == null)
                return false;

            return _assessmentDto.SelectedSimulation.Certification.Title.Contains("2022");
        }

        public string A2G21bOrA221b()
        {
            if (IsNcc2022Certification())
                return "A2G2(1)(b)";
            else
                return "A2.2(1)(b)";
        }

        public string A2G22biiOrA222bii()
        {
            if (IsNcc2022Certification())
                return "A2G2(2)(b)(ii)";
            else
                return "A2.2(2)(b)(ii)";
        }

        public string A2G22dOrA222d()
        {
            if (IsNcc2022Certification())
                return "A2G2(2)(d)";
            else
                return "A2.2(2)(d)";
        }

        public string H6Or26()
        {
            if (IsNcc2022Certification())
                return "H6";
            else
                return "2.6";
        }

        public string H6P2OrP262()
        {
            if (IsNcc2022Certification())
                return "H6P2";
            else
                return "P2.6.2";
        }

        public string H6P1OrP261()
        {
            if (IsNcc2022Certification())
                return "H6P1";
            else
                return "P2.6.1";
        }

        public string H6V2OrV2622()
        {
            if (IsNcc2022Certification())
                return "H6V2";
            else
                return "V2.6.2.2";
        }

        public string Specification42OrPart31201()
        {
            if (IsNcc2022Certification())
                return "Specification 42";
            else
                return "Part 3.12.0.1";
        }

        public string H6D21AOr3120ai()
        {
            if (IsNcc2022Certification())
                return "H6D2(1)(a)";
            else
                return "3.12.0(a)(i)";
        }

        public string H6D21BOr3120aii()
        {
            if (IsNcc2022Certification())
                return "H6D2(1)(b)";
            else
                return "3.12.0(a)(ii)";
        }

        public string H6D22BOr3120b()
        {
            if (IsNcc2022Certification())
            {
                if(_assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMHouseEnergyRating.ToString("G"))
                    return "H6D2(2)(a)";

                return "H6D2(2)(b)";
            }
            else
                return "3.12.0(b)";
        }

        public string N1322Or31211()
        {
            if (IsNcc2022Certification())
                return "13.2.2";
            else
                return "3.12.1.1";
        }

        public string EnergyAnalysisOrHer()
        {
            if (IsComparisonDtsHer || IsComplianceMethod("CMPerfWAProtocolHER"))
                return "house energy rating";
            else
                return "energy analysis";
        }

        public string ProposedConditionedFloorArea
        {
            get
            {
                return _assessmentDto.SelectedSimulation.Proposed.ConditionedFloorArea?.ToString("0.0") ?? "-";
            }
        }
        public string ReferenceConditionedFloorArea
        {
            get
            {
                return _assessmentDto.SelectedSimulation.Reference.ConditionedFloorArea?.ToString("0.0") ?? "-";
            }
        }

        public string ProposedUnconditionedFloorArea
        {
            get { return _assessmentDto.SelectedSimulation.Proposed.UnconditionedFloorArea?.ToString("0.0") ?? "-"; }
        }
        public string ReferenceUnconditionedFloorArea
        {
            get { return _assessmentDto.SelectedSimulation.Reference.UnconditionedFloorArea?.ToString("0.0") ?? "-"; }
        }

        public string ProposedAttachedGarageFloorArea
        {
            get { return _assessmentDto.SelectedSimulation.Proposed.AttachedGarageFloorArea?.ToString("0.0") ?? "-"; }
        }
        public string ReferenceAttachedGarageFloorArea
        {
            get { return _assessmentDto.SelectedSimulation.Reference.AttachedGarageFloorArea?.ToString("0.0") ?? "-"; }
        }

        public string ProposedTotalFloorArea
        {
            get { return ((_assessmentDto.SelectedSimulation.Proposed.ConditionedFloorArea ?? 0) + (_assessmentDto.SelectedSimulation.Proposed.UnconditionedFloorArea ?? 0)).ToString("0.0"); }
        }
        public string ReferenceTotalFloorArea
        {
            get { return ((_assessmentDto.SelectedSimulation.Reference.ConditionedFloorArea ?? 0) + (_assessmentDto.SelectedSimulation.Reference.UnconditionedFloorArea ?? 0)).ToString("0.0"); }
        }

        public string ProposedHabitableFloorArea
        {
            get
            {
                var habitableZones = _assessmentDto.SelectedSimulation.Proposed.Zones
                    ?.Where(z => z.ZoneType?.ZoneTypeCode == "ZTHabitableRoom");

                var area = habitableZones
                    .Select(x => x.FloorArea)
                    .Aggregate(0m, (a, b) => a + b ?? 0);

                return area.ToString("0.00");
            }
        }

        public string ProposedHeating
        {
            get
            {
                if (!_assessmentDto.SelectedSimulation.Proposed.Heating.HasValue)
                {
                    return "-";
                }

                if (_assessmentDto.SelectedSimulation.Proposed.Heating.Value == -1)
                    return "N/A";

                return _assessmentDto.SelectedSimulation.Proposed.Heating.Value.ToString("0.0");
            }
        }

        public string FinalEnergyLoadUnits
        {
            get
            {
                if (string.IsNullOrWhiteSpace(_assessmentDto.SelectedSimulation.AssessmentSoftware?.EnergyLoadUnits))
                    return "-";

                return _assessmentDto.SelectedSimulation.AssessmentSoftware?.EnergyLoadUnits + "/m<sup>2</sup>";
            }
        }

        // Final Description - Construction
        public string FinalDescription(ConstructionTemplateDto parent)
        {
            return parent.OverrideDisplayDescription ??
                   parent.DisplayDescription ??
                   parent.Description;
        }

        // Final Description - Services
        public string FinalDescription(ServiceTemplateDto parent)
        {
            // Special: 'Cooking' (from 'Cooktop' and 'Oven')
            if (parent.ServiceCategory.ServiceCategoryCode == "Cooking")
            {
                return parent.Description;
            }
            else
            {
                return (parent.Volume > 0
                        ? $"{Volume(parent)}L "
                        : "")
                    +  (parent.OverrideDisplayDescription ?? parent.DisplayDescription ?? parent.Description)
                    + (parent.SystemCapacity > 0
                        ? $", {SystemCapacity(parent)}kW array"
                        : "")
                    + (parent.InverterCapacity > 0
                        ? $", {InverterCapacity(parent)}kW inverter"
                        : "")
                    + (parent.BatteryCapacity > 0 && parent.ServiceBatteryType != null && parent.ServiceBatteryType.ServiceBatteryTypeCode != "None"
                        ? $", {BatteryCapacity(parent).ToLower()}kWh " + (parent.ServiceBatteryType?.Title != null ? $"{parent.ServiceBatteryType?.Title?.ToLower()} battery" : "battery")
                        : "")
                    + (parent.ServicePumpType != null
                        ? $", {PumpType(parent).ToLower()} pump"
                        : "")
                    + (parent.StarRating2019 > 0
                        ? (", " + StarRating2019(parent, true) + " star energy rating" + (parent.ServiceType?.ServiceTypeCode == "HeatPumpDucted" || parent.ServiceType?.ServiceTypeCode == "HeatPumpNonDucted" ? " (GEMS 2019)" : ""))
                        : "");
            }
        }
        public string FinalDisplayDescription(ServiceTemplateDto parent)
        {
            return parent.OverrideDisplayDescription ?? parent.DisplayDescription;
        }

        public string FinalSummaryDescriptionWithExtraDetails(ConstructionTemplateDto construction)
        {
            var desc = FinalDescription(construction);

            if(IncludeInsulation(construction.Category))
                desc += $", {InsulationDescription((SurfaceTemplateDto)construction)}";

            if (IsARoof(construction.Category))
                desc += $", SA = {Round2DP(((SurfaceTemplateDto)construction).ExteriorSolarAbsorptance)}";

            return desc;
        }

        public string CooktopOvenFinalDescription(ServiceTemplateDto parent)
        {
            return (parent.OverrideDisplayDescription ?? parent.DisplayDescription)
                + (parent.StarRating2019 > 0
                    ? (" (" + StarRating2019(parent, true) + " star energy rating)" + (parent.ServiceType?.ServiceTypeCode == "HeatPumpDucted" || parent.ServiceType?.ServiceTypeCode == "HeatPumpNonDucted" ? " (GEMS 2019)" : ""))
                    : "");
        }

        public decimal? ProposedHeatingValue
        {
            get
            {
                return _assessmentDto.SelectedSimulation.Proposed.Heating.Value;
            }
        }

        public string ReferenceHeating
        {
            get
            {
                if (!_assessmentDto.SelectedSimulation.Reference.Heating.HasValue)
                {
                    return "-";
                }

                if (_assessmentDto.SelectedSimulation.Reference.Heating.Value == -1)
                    return "N/A";

                return _assessmentDto.SelectedSimulation.Reference.Heating.Value.ToString("0.0");
            }
        }

        public decimal? ReferenceHeatingValue
        {
            get
            {
                return _assessmentDto.SelectedSimulation.Reference.Heating;
            }
        }

        public string ProposedCooling
        {
            get
            {
                if (!_assessmentDto.SelectedSimulation.Proposed.Cooling.HasValue)
                {
                    return "-";
                }

                if (_assessmentDto.SelectedSimulation.Proposed.Cooling.Value == -1)
                    return "N/A";

                return _assessmentDto.SelectedSimulation.Proposed.Cooling.Value.ToString("0.0");
            }
        }

        public decimal? ProposedCoolingValue
        {
            get
            {
                return _assessmentDto.SelectedSimulation.Proposed.Cooling;
            }
        }

        public string ReferenceCooling
        {
            get
            {
                if (!_assessmentDto.SelectedSimulation.Reference.Cooling.HasValue)
                {
                    return "-";
                }

                if (_assessmentDto.SelectedSimulation.Reference.Cooling.Value == -1)
                    return "N/A";

                return _assessmentDto.SelectedSimulation.Reference.Cooling.Value.ToString("0.0");
            }
        }

        public decimal? ReferenceCoolingValue
        {
            get
            {
                return _assessmentDto.SelectedSimulation.Reference.Cooling;
            }
        }

        public string ProposedTotalThermalPerformanceLoads
        {
            get
            {
                if (_assessmentDto.SelectedSimulation.Proposed.TotalEnergyLoad.HasValue == false)
                {
                    return "-";
                }

                if (_assessmentDto.SelectedSimulation.Proposed.TotalEnergyLoad.Value == -1)
                    return "N/A";

                return (_assessmentDto.SelectedSimulation.Proposed.TotalEnergyLoad.Value).ToString("0.0");
            }
        }

        public string ReferenceTotalThermalPerformanceLoads
        {
            get
            {
                if (_assessmentDto.SelectedSimulation.Reference.TotalEnergyLoad.HasValue == false)
                {
                    return "-";
                }

                if (_assessmentDto.SelectedSimulation.Reference.TotalEnergyLoad.Value == -1)
                    return "N/A";

                return (_assessmentDto.SelectedSimulation.Reference.TotalEnergyLoad.Value).ToString("0.0");
            }
        }

        public bool IsMinimumHouseEnergyRatingRequired
        {
            get
            {
                if (_assessmentDto.SelectedSimulation.RequiredHouseEnergyRating == -1)
                {
                    return false;
                }

                return true;
            }
        }

        public string RequiredHouseEnergyRating
        {
            get
            {
                if (!_assessmentDto.SelectedSimulation.RequiredHouseEnergyRating.HasValue)
                {
                    return "-";
                }
                return _assessmentDto.SelectedSimulation.RequiredHouseEnergyRating.Value.ToString("0.0");
            }
        }

        public string ProposedHouseEnergyRating
        {
            get
            {
                if (_assessmentDto.SelectedSimulation.Proposed.OverrideEnergyLoads == false)
                {
                    if (!_assessmentDto.SelectedSimulation.Proposed.HouseEnergyRating.HasValue)
                    {
                        return "-";
                    }
                    return _assessmentDto.SelectedSimulation.Proposed.HouseEnergyRating.Value.ToString("0.0");
                }
                else
                {
                    if (!_assessmentDto.SelectedSimulation.Proposed.HouseEnergyRatingOverride.HasValue)
                    {
                        return "-";
                    }

                    return _assessmentDto.SelectedSimulation.Proposed.HouseEnergyRatingOverride.Value.ToString("0.0");
                }
            }
        }

        public string ReferenceHouseEnergyRating
        {
            get
            {
                if (_assessmentDto.SelectedSimulation.Reference.OverrideEnergyLoads == false)
                {
                    if (!_assessmentDto.SelectedSimulation.Reference.HouseEnergyRating.HasValue)
                    {
                        return "-";
                    }
                    return _assessmentDto.SelectedSimulation.Reference.HouseEnergyRating.Value.ToString("0.0");
                }
                else
                {
                    if (!_assessmentDto.SelectedSimulation.Reference.HouseEnergyRatingOverride.HasValue)
                    {
                        return "-";
                    }

                    return _assessmentDto.SelectedSimulation.Reference.HouseEnergyRatingOverride.Value.ToString("0.0");
                }
            }
        }

        public string SoftwareName
        {
            get
            {
                return _assessmentDto.SelectedSimulation.AssessmentSoftware?.Description;
            }
        }

        public bool ShowLoadLimitsEnabled => _assessmentDto.SelectedSimulation.HeatingAndCoolingRulesetCode == "Enabled";

        public bool ShowReferenceEnergy => (_assessmentDto.SelectedSimulation.ComplianceMethod.RequiresReferenceBuilding.HasValue &&
                                            _assessmentDto.SelectedSimulation.ComplianceMethod.RequiresReferenceBuilding.Value == true)
                                           ||
                                           _assessmentDto.SelectedSimulation.HeatingAndCoolingRulesetCode == "Enabled"
                                           ||
                                           IsComplianceMethod("CMPerfELL");

        public string ShowReferenceEnergyStr => ShowReferenceEnergy ? "true" : "false";

        public bool ShowReferenceFloorArea =>
            (_assessmentDto.SelectedSimulation.ComplianceMethod.RequiresReferenceBuilding.HasValue &&
             _assessmentDto.SelectedSimulation.ComplianceMethod.RequiresReferenceBuilding.Value == true);

        public string ShowReferenceFloorAreaStr => ShowReferenceFloorArea ? "true" : "false";


        public string SoftwareCode
        {
            get
            {
                return _assessmentDto.SelectedSimulation.AssessmentSoftware?.AssessmentSoftwareCode;
            }
        }

        // NOTE: Duplicate (search 'Units')
        public string SoftwareEnergyLoadUnits
        {
            get
            {
                return _assessmentDto.SelectedSimulation.AssessmentSoftware?.EnergyLoadUnits + "/m<sup>2</sup>";
            }
        }

        public string PerformanceRequirementP261
        {
            get
            {
                return _assessmentDto.PerformanceRequirementP261Description;
            }
        }

        public string PerformanceRequirementP262
        {
            get
            {
                if (_assessmentDto.PerformanceRequirementP262Description == null)
                {
                    return string.Empty;
                }
                return _assessmentDto.PerformanceRequirementP262Description;
            }
        }

        public string ComplianceStatusCode
        {
            get
            {
                if (_assessmentDto.ComplianceStatusCode == null)
                {
                    return string.Empty;
                }
                return _assessmentDto.ComplianceStatusCode;
            }
        }

        public string ComplianceStatus
        {
            get
            {
                if (_assessmentDto.ComplianceStatusDescription == null)
                {
                    return string.Empty;
                }
                return _assessmentDto.ComplianceStatusDescription;
            }
        }

        public string ReportCertificateNumber
        {
            get
            {
                if (_assessmentDto.CertificateNumber == null)
                {
                    return string.Empty;
                }
                return _assessmentDto.CertificateNumber;
            }
        }

        public string LightAndVentilationReportCertificateNumber
        {
            get
            {
                if (_assessmentDto.LightAndVentilationCertificateNumber == null)
                {
                    return string.Empty;
                }
                return _assessmentDto.LightAndVentilationCertificateNumber;
            }
        }

        public string ReportCertificateDate
        {
            get
            {
                if (_assessmentDto.CertificateDateOverride.HasValue)
                {
                    // Convert UTC date to local timezone to avoid timezone issues
                    var localDate = UtilityFunctions.GetUtcAsStandardTime(_assessmentDto.CertificateDateOverride.Value);
                    return localDate.ToString("dd MMMM yyyy");
                }
                else if (_assessmentDto.CerficateDate.HasValue)
                {
                    // Convert UTC date to local timezone to avoid timezone issues
                    var localDate = UtilityFunctions.GetUtcAsStandardTime(_assessmentDto.CerficateDate.Value);
                    return localDate.ToString("dd MMMM yyyy");
                }
                else
                {
                    return string.Empty;
                }
            }
        }

        public string ReportDocumentVersion
        {
            get { return _assessmentDto.AssessmentProjectDetail?.AssessmentVersion.ToString("0.00"); }
        }

        public string ReportAssessorName
        {
            get
            {
                // Check if compliance method has PreparedByOverrideUserId
                if (_assessmentDto.SelectedSimulation?.ComplianceMethod?.PreparedByOverrideUserId.HasValue == true)
                {
                    var overrideUser = _unitOfWork.Context.RSS_User
                        .FirstOrDefault(u => u.UserId == _assessmentDto.SelectedSimulation.ComplianceMethod.PreparedByOverrideUserId.Value);
                    if (overrideUser != null)
                    {
                        return overrideUser.FullName;
                    }
                }

                // Fall back to original logic
                if (string.IsNullOrEmpty(_assessmentDto.AssessorUser?.FullName))
                {
                    if (_lastAssignedAssessor != null)
                    {
                        return _lastAssignedAssessor.FullName;
                    }

                    return string.Empty;
                }
                return _assessmentDto.AssessorUser?.FullName;
            }
        }

        public string ReportAssessorSignature
        {
            get
            {
                string assessorSignature = ReportAssessorSignatureSvg;
                if (string.IsNullOrEmpty(assessorSignature))
                    assessorSignature = ReportAssessorSignatureBase64;
                return assessorSignature;
            }
        }

        public string ReportAssessorSignatureSvg
        {
            get
            {
                // Check if compliance method has PreparedByOverrideUserId
                if (_assessmentDto.SelectedSimulation?.ComplianceMethod?.PreparedByOverrideUserId.HasValue == true)
                {
                    var overrideUser = _unitOfWork.Context.RSS_User
                        .FirstOrDefault(u => u.UserId == _assessmentDto.SelectedSimulation.ComplianceMethod.PreparedByOverrideUserId.Value);
                    if (overrideUser != null)
                    {
                        // If override user exists, return their signature or empty if they don't have one
                        return !string.IsNullOrEmpty(overrideUser.SignatureSVGImage) ? overrideUser.SignatureSVGImage : string.Empty;
                    }
                }

                // Fall back to original logic only if no override user
                if (_assessmentDto.AssessorUser == null || string.IsNullOrEmpty(_assessmentDto.AssessorUser.SignatureSVGImage))
                {
                    if (_lastAssignedAssessor != null && !string.IsNullOrEmpty(_lastAssignedAssessor.SignatureSVGImage))
                    {
                        return _lastAssignedAssessor.SignatureSVGImage;
                    }
                    return string.Empty;
                }
                return _assessmentDto.AssessorUser.SignatureSVGImage;
            }
        }

        public string ReportAssessorSignatureBase64
        {
            get
            {
                // Check if compliance method has PreparedByOverrideUserId
                if (_assessmentDto.SelectedSimulation?.ComplianceMethod?.PreparedByOverrideUserId.HasValue == true)
                {
                    var overrideUser = _unitOfWork.Context.RSS_User
                        .FirstOrDefault(u => u.UserId == _assessmentDto.SelectedSimulation.ComplianceMethod.PreparedByOverrideUserId.Value);
                    if (overrideUser != null)
                    {
                        // If override user exists, return their signature or empty if they don't have one
                        return !string.IsNullOrEmpty(overrideUser.SignatureBase64Image)
                            ? "<img src=\"" + overrideUser.SignatureBase64Image + "\" />"
                            : string.Empty;
                    }
                }

                // Fall back to original logic only if no override user
                if (_assessmentDto.AssessorUser == null || string.IsNullOrEmpty(_assessmentDto.AssessorUser.SignatureBase64Image))
                {
                    if (_lastAssignedAssessor != null && !string.IsNullOrEmpty(_lastAssignedAssessor.SignatureBase64Image))
                    {
                        return "<img src=\"" + _lastAssignedAssessor.SignatureBase64Image + "\" />";
                    }
                    return string.Empty;
                }
                return "<img src=\"" + _assessmentDto.AssessorUser.SignatureBase64Image + "\" />";
            }
        }

        public List<AssessmentDrawingDto> Drawings
        {
            get
            {
                List<AssessmentDrawingDto> list;

                // Always return the baseline drawings UNLESS an option has been selected AND the option requires new drawings.
                if (_assessmentDto.SelectedSimulation != _assessmentDto.BaselineSimulation &&
                    (_assessmentDto.SelectedSimulation.UpdatedDrawingsRequired.HasValue && _assessmentDto.SelectedSimulation.UpdatedDrawingsRequired.Value))
                    list = _assessmentDto.SelectedSimulation.AssessmentDrawings;
                else
                    list = _assessmentDto.BaselineSimulation.AssessmentDrawings;


                // Filter by our criteria.
                var filtered = list.Where(drawing => drawing.Attachment != null &&
                                          !string.IsNullOrEmpty(drawing.Attachment.URL) &&
                                          drawing.IsIncludedInReport).ToList();

                return filtered;
            }
        }


        public string GeneratedStampPath { get; set; }
        public string GeneratedPreliminaryWatermark { get; set; }

        public List<ConstructionCategoryDto> ConstructionCategories()
        {
            var categories = _constructionFactory().GetConstructionCategoryList();
            return categories;
        }

        public List<ConstructionCategoryDto> ConstructionTabCategories()
        {
            var categories = _constructionFactory().GetConstructionCategoryList();
            List<ConstructionCategoryDto> constructionCategories = categories
               .Where(x => x.Type == "surface" &&
                    (
                        x.ConstructionCategoryCode != "ExteriorDoor" &&
                        x.ConstructionCategoryCode != "InteriorDoor"
                    )
               )
               .ToList();
            return constructionCategories;
        }

        public List<ConstructionCategoryDto> OpeningsTabCategories()
        {
            var categories = _constructionFactory().GetConstructionCategoryList();
            List<ConstructionCategoryDto> openingCategories = categories
               .Where(x => x.Type != "surface" ||
                    (
                        x.ConstructionCategoryCode == "ExteriorDoor" ||
                        x.ConstructionCategoryCode == "InteriorDoor" ||
                        x.ConstructionCategoryCode == "PermanentOpening"
                    )
               )
               .ToList();
            return openingCategories;
        }

        public List<ServiceTemplateDto> ServicesInCategory(
            AssessmentComplianceBuildingDto building,
            string category
        )
        {
            return building.Services?
                .Where(x => x.ServiceCategory.ServiceCategoryCode == category &&
                            x.ShowInReport == true)
                .ToList();
        }

        public bool BuildingHasOpenings(AssessmentComplianceBuildingDto building)
        {
            return building.Openings
                       .Where(x => x.ShowInReport == true)
                       .Count() > 0
                   ||
                   building.Surfaces
                       .Where(x => x.ShowInReport == true &&
                                   (x.Category.ConstructionCategoryCode == "ExteriorDoor" ||
                                    x.Category.ConstructionCategoryCode == "InteriorDoor"))
                       .Count() > 0;
        }

        public bool BuildingHasServices(AssessmentComplianceBuildingDto building)
        {
            return building.Services?
                .Where(x => x.ShowInReport == true)
                .Count() > 0;
        }

        public bool BuildingHasSurfaces(AssessmentComplianceBuildingDto building)
        {
            return building.Surfaces
                .Where(x => x.ShowInReport == true &&
                            x.Category.ConstructionCategoryCode != "ExteriorDoor" &&
                            x.Category.ConstructionCategoryCode != "InteriorDoor")
                .Count() > 0;
        }

        public bool CategoryHasServices(
            AssessmentComplianceBuildingDto building,
            string category)
        {
            return building.Services?
                .Where(x => x.ServiceCategory.ServiceCategoryCode == category &&
                       x.ShowInReport == true)
                .Count() > 0;
        }

        public string NumberOfElements(ConstructionTemplateDto construction)
        {

            if (construction.GetType() == typeof(SurfaceTemplateDto))
            {
                var surface = (SurfaceTemplateDto) construction;

                if (surface.Elements != null)
                    return surface.Elements.Count.ToString();
            }
            else if (construction.GetType() == typeof(OpeningTemplateDto))
            {
                var opening = (OpeningTemplateDto) construction;

                if (opening.Elements != null)
                    return opening.Elements.Count.ToString();
            }

            return "1"; // Parent element only.
        }

        public bool ShowBattery(AssessmentComplianceBuildingDto building, string category)
        {
            return ServicesInCategory(building, category).Any(x => x.ServiceBatteryType.ServiceBatteryTypeCode != null && x.ServiceBatteryType.ServiceBatteryTypeCode != "None");
        }

        // Service
        public string NumberOfElements(ServiceTemplateDto construction)
        {
            if (construction != null && construction.Elements != null)
                return construction.Elements.Count.ToString();
            else
                return "1";
        }

        // Service fields
        public string ServiceType(ServiceTemplateDto service)
        {
            return service.ServiceType?.Title ?? "-";
        }
        public string FuelType(ServiceTemplateDto service)
        {
            return service.ServiceFuelType?.Title ?? "-";
        }
        public string PumpType(ServiceTemplateDto service)
        {
            return service.ServicePumpType?.Title ?? "-";
        }
        public string BatteryType(ServiceTemplateDto service)
        {
            return (service.ServiceBatteryType != null && service.ServiceBatteryType?.Title != "None") ? service.ServiceBatteryType?.Title : "-";
        }
        public string StarRating2019(ServiceTemplateDto service, bool valueOnly = false)
        {
            return service.StarRating2019 != null && service.StarRating2019 > 0
                   ? string.Format("{0:#,##0.#}", service.StarRating2019) + (!valueOnly  ? (" stars" + (service.ServiceType?.ServiceTypeCode == "HeatPumpDucted" || service.ServiceType?.ServiceTypeCode == "HeatPumpNonDucted" ? " (GEMS 2019)" : "")) : "" )
                   : "-";
        }
        public string IsDucted(ServiceTemplateDto service)
        {
            return service.IsDucted != null ? service.IsDucted.ToString() : "-";
        }
        public string IsFlued(ServiceTemplateDto service)
        {
            return service.IsFlued != null ? service.IsFlued.ToString() : "-";
        }
        public string LampPowerRating(ServiceTemplateDto service)
        {
            return service.LampPowerRating != null ? string.Format("{0:#,##0.0}", service.LampPowerRating) : "-";
        }
        public string IsRecessed(ServiceTemplateDto service)
        {
            return service.IsRecessed != null ? service.IsRecessed.ToString() : "-";
        }
        public string IsSealed(ServiceTemplateDto service)
        {
            return service.IsSealed != null ? ((bool)service.IsSealed ? "Sealed" : "Unsealed") : "-";
        }
        public string ICRating(ServiceTemplateDto service)
        {
            return service.ICRating?.Title ?? "-";

        }
        public string CutOutDiameter(ServiceTemplateDto service)
        {
            return service.CutOutDiameter != null ? string.Format("{0:#,##0}", service.CutOutDiameter) : "-";
        }
        public string Length(ServiceTemplateDto service)
        {
            return service.Length != null ?string.Format("{0:#,##0}", service.Length) : "-";
        }
        public string Width(ServiceTemplateDto service)
        {
            return service.Width != null ? string.Format("{0:#,##0}", service.Width) : "-";
        }
        public string BladeDiameter(ServiceTemplateDto service)
        {
            return service.BladeDiameter != null ? string.Format("{0:#,##0}", service.BladeDiameter) : "-";
        }
        public string IsPermanentlyInstalled(ServiceTemplateDto service)
        {
            return service.IsPermanentlyInstalled != null ? service.IsPermanentlyInstalled.ToString() : "-";
        }
        public string HasSpeedController(ServiceTemplateDto service)
        {
            return service.HasSpeedController != null ? service.HasSpeedController.ToString() : "-";
        }
        public string SystemCapacity(ServiceTemplateDto service)
        {
            return service.SystemCapacity != null ? string.Format("{0:#,##0.0}", service.SystemCapacity) : "-";
        }
        public string InverterCapacity(ServiceTemplateDto service)
        {
            return service.InverterCapacity != null ? string.Format("{0:#,##0.0}", service.InverterCapacity) : "-";
        }
        public string BatteryCapacity(ServiceTemplateDto service)
        {
            return service.BatteryCapacity != null && service.ServiceBatteryType?.Title != "None" ? string.Format("{0:#,##0.0}", service.BatteryCapacity) : "-";
        }
        public string Volume(ServiceTemplateDto service)
        {
            return service.Volume != null ?  string.Format("{0:#,##0}", service.Volume) : "-";
        }
        public string HeatingSystemType(ServiceTemplateDto service)
        {
            return service.HeatingSystemType?.Title != null ? service.HeatingSystemType?.Title.ToString() : "-";
        }
        public string HasCover(ServiceTemplateDto service)
        {
            return service.HasCover != null ? service.HasCover.ToString() : "-";
        }
        public string HasTimeSwitch(ServiceTemplateDto service)
        {
            return service.HasTimeSwitch != null ? service.HasTimeSwitch.ToString() : "-";
        }

        /* Area Sums */
        public string NumberSum(ServiceTemplateDto group)
        {
            return group.Elements.Select(x => x.Number).Aggregate(0, (a, b) => a + b).ToString("N0");
        }
        public string CutOutAreaSum(ServiceTemplateDto group)
        {
            return group.Elements.Select(x => x.CutoutArea).Aggregate(0m, (a, b) => a + b).ToString("N6");
        }
        public string PanelAreaSum(ServiceTemplateDto group)
        {
            return group.Elements.Select(x => x.PanelArea).Aggregate(0m, (a, b) => a + b).ToString("N2");
        }

        public List<ConstructionCategoryDto> SummaryConstructionCategories()
        {
            // Only want to return one of the grouped categories: (Other function will pull in the items from both).
            var categories = _constructionFactory().GetConstructionCategoryList();
            // Hardcode the codes so newly created Categories won't appear in the summary list
            List<ConstructionCategoryDto> filteredCategories = categories
                .Where(x => x.ConstructionCategoryCode == "Roof" ||
                    x.ConstructionCategoryCode == "CeilingRoofAbove" ||
                    x.ConstructionCategoryCode == "ExteriorWall" ||
                    x.ConstructionCategoryCode == "SubfloorWall" ||
                    x.ConstructionCategoryCode == "InteriorWall" ||
                    x.ConstructionCategoryCode == "GroundFloor" ||
                    x.ConstructionCategoryCode == "ExteriorFloorElevated" ||
                    x.ConstructionCategoryCode == "IntermediateFloor"
                )
                .ToList();
            return filteredCategories;
        }
        public List<ConstructionCategoryDto> SummaryOpeningsCategories()
        {
            var categories = _constructionFactory().GetConstructionCategoryList();
            // Hardcode the codes so newly created Categories won't appear in the summary list
            List<ConstructionCategoryDto> filteredCategories = categories
                .Where(x => x.ConstructionCategoryCode == "ExteriorGlazing" ||
                    x.ConstructionCategoryCode == "ExteriorDoor" ||
                    x.ConstructionCategoryCode == "Skylight" ||
                    x.ConstructionCategoryCode == "RoofWindow" ||
                    x.ConstructionCategoryCode == "InteriorDoor" ||
                    x.ConstructionCategoryCode == "HorizontalOpening" ||
                    x.ConstructionCategoryCode == "VerticalOpening"
                ).
                ToList();
            return filteredCategories;
        }
        public List<ServiceCategoryDto> SummaryServicesCategories()
        {
            var categories = _serviceFactory().GetServiceCategories();
            // Hardcode the codes so newly created Categories won't appear in the summary list
            List<ServiceCategoryDto> filteredCategories = categories
                .Where(x => x.ServiceCategoryCode == "SpaceHeatingSystem" ||
                    x.ServiceCategoryCode == "SpaceCoolingSystem" ||
                    x.ServiceCategoryCode == "HotWaterSystem" ||
                    x.ServiceCategoryCode == "PhotovoltaicSystem" ||
                    x.ServiceCategoryCode == "SwimmingPool" ||
                    x.ServiceCategoryCode == "Spa" ||
                    x.ServiceCategoryCode == "Cooktop" ||
                    x.ServiceCategoryCode == "Oven"
                ).
                ToList();
            // Combine 'Cooktop' and 'Oven'
            ServiceCategoryDto cooktopCategory = filteredCategories.First(x => x.ServiceCategoryCode == "Cooktop");
            ServiceCategoryDto ovenCategory = filteredCategories.First(x => x.ServiceCategoryCode == "Oven");
            ServiceCategoryDto newCookingCategory = buildCookingCategory(
                filteredCategories.First(x => x.ServiceCategoryCode == "Cooktop"),
                filteredCategories.First(x => x.ServiceCategoryCode == "Oven")
            );
            if (newCookingCategory != null)
            {
                filteredCategories = filteredCategories.Where(x => x.ServiceCategoryCode != "Cooktop" && x.ServiceCategoryCode != "Oven").ToList();
                filteredCategories.Add(newCookingCategory);
                filteredCategories.Sort((a,b) => a.SortOrder > b.SortOrder ? 1 : -1);
            }
            // Return
            return filteredCategories;
        }

        public ServiceCategoryDto buildCookingCategory(ServiceCategoryDto cooktopCategory, ServiceCategoryDto ovenCategory)
        {
            bool cooktopHasServices = CategoryHasServices(ProposedBuilding, "Cooktop");
            bool ovenHasServices = CategoryHasServices(ProposedBuilding, "Oven");
            if (!cooktopHasServices && !ovenHasServices)
            {
                return null;
            }
            ServiceCategoryDto newCookingCategory = new ServiceCategoryDto
            {
                ServiceCategoryCode = "Cooking",
                Title = "Cooking Appliances",
                SortOrder = 35
            };
            if (cooktopHasServices && ovenHasServices)
                newCookingCategory.Description = $"{ CooktopOvenFinalDescription(ServicesForCategory(ProposedBuilding, cooktopCategory)?.Find(x => x.IsPrimary)) }, { CooktopOvenFinalDescription(ServicesForCategory(ProposedBuilding, ovenCategory)?.Find(x => x.IsPrimary)) }";
            else if (cooktopHasServices)
                newCookingCategory.Description = $"{ CooktopOvenFinalDescription(ServicesForCategory(ProposedBuilding, cooktopCategory)?.Find(x => x.IsPrimary)) }";
            else if (ovenHasServices)
                newCookingCategory.Description = $"{ CooktopOvenFinalDescription(ServicesForCategory(ProposedBuilding, ovenCategory)?.Find(x => x.IsPrimary)) }";
            return newCookingCategory;
        }

        public List<ServiceCategoryDto> ServiceCategories()
        {
            var categories = _serviceFactory().GetServiceCategories();
            return categories;
        }

        public bool CategoryHasItems(
            AssessmentComplianceBuildingDto building,
            ConstructionCategoryDto category)
        {
            if (category.Type == "surface" || category.Type == "permanentopening")
                return building.Surfaces
                    .Where(x => x.Category.ConstructionCategoryCode == category.ConstructionCategoryCode
                                && x.ShowInReport == true)
                    .Count() > 0;
            else
                return building.Openings
                    .Where(x => x.Category.ConstructionCategoryCode == category.ConstructionCategoryCode
                                && x.ShowInReport == true)
                    .Count() > 0;
        }

        public List<ConstructionTemplateDto> ConstructionsForCategory(
            AssessmentComplianceBuildingDto building,
            ConstructionCategoryDto category)
        {
            if (category.Type == "surface" || category.Type == "permanentopening")
                return building.Surfaces
                    .Select(x => x as ConstructionTemplateDto)
                    .Where(x => x.Category.ConstructionCategoryCode == category.ConstructionCategoryCode &&
                                x.ShowInReport == true)
                    .ToList();
            else
                return building.Openings
                    .Select(x => x as ConstructionTemplateDto)
                    .Where(x => x.Category.ConstructionCategoryCode == category.ConstructionCategoryCode &&
                                x.ShowInReport == true)
                    .ToList();
        }

        public List<ConstructionTemplateDto> ConstructionsGroupedForCategory(
            AssessmentComplianceBuildingDto building,
            ConstructionCategoryDto category)
        {
            if (category.Type == "surface" || category.Type == "permanentopening")
            {
                // *Assuming* Ceiling (Roof/Roof Space Above) and Ceiling (Neighbour Above) should be grouped
                if (category.ConstructionCategoryCode == "CeilingRoofAbove")
                {
                    List<ConstructionTemplateDto> surfaces = building.Surfaces
                        .Select(x => x as ConstructionTemplateDto)
                        .Where(x => (x.Category.ConstructionCategoryCode == "CeilingRoofAbove" ||
                            x.Category.ConstructionCategoryCode == "CeilingNeighbourAbove") &&
                            x.ShowInReport == true)
                        .OrderBy(x => x.Category.ConstructionCategoryCode)
                        .ToList();
                    return surfaces;
                }
                else if (category.ConstructionCategoryCode == "GroundFloor")
                {
                    List<ConstructionTemplateDto> surfaces = building.Surfaces
                        .Select(x => x as ConstructionTemplateDto)
                        .Where(x => (x.Category.ConstructionCategoryCode == "GroundFloor" ||
                                     x.Category.ConstructionCategoryCode == "ExteriorFloor") &&
                                    x.ShowInReport == true)
                        .OrderBy(x => x.Category.ConstructionCategoryCode)
                        .ToList();
                    return surfaces;
                }
                else if (category.ConstructionCategoryCode == "ExteriorFloorElevated")
                {
                    List<ConstructionTemplateDto> surfaces = building.Surfaces
                        .Select(x => x as ConstructionTemplateDto)
                        .Where(x => x.Category.ConstructionCategoryCode == "ExteriorFloorElevated" &&
                            x.ShowInReport == true)
                        .ToList();
                    return surfaces;
                }
                // Interior Wall and Interior Wall (Adjacent to Neighbour) should be grouped
                else if (category.ConstructionCategoryCode == "InteriorWall")
                {

                    List<ConstructionTemplateDto> surfaces = building.Surfaces
                        .Select(x => x as ConstructionTemplateDto)
                        .Where(x => (x.Category.ConstructionCategoryCode == "InteriorWall" ||
                                     x.Category.ConstructionCategoryCode == "InteriorWallAdjacentToNeighbour" ||
                                     x.Category.ConstructionCategoryCode == "InteriorWallAdjacentToRoofSpace" ||
                                     x.Category.ConstructionCategoryCode == "InteriorWallAdjacentToSubfloorSpace") &&
                            x.ShowInReport == true)
                        .OrderBy(x => x.Category.ConstructionCategoryCode)
                        .ToList();
                    return surfaces;
                }
                // Intermediate Floor and Intermediate Floor (Neighbour Below) should be grouped
                else if (category.ConstructionCategoryCode == "IntermediateFloor")
                {
                    List<ConstructionTemplateDto> surfaces = building.Surfaces
                        .Select(x => x as ConstructionTemplateDto)
                        .Where(x => (x.Category.ConstructionCategoryCode == "IntermediateFloor" ||
                            x.Category.ConstructionCategoryCode == "IntermediateFloorNeighbourBelow") &&
                            x.ShowInReport == true)
                        .ToList();
                    return surfaces;
                }
                // Ignore the groups partner categories
                else if (category.ConstructionCategoryCode == "CeilingNeighbourAbove" ||
                    category.ConstructionCategoryCode == "ExteriorFloor" ||
                    category.ConstructionCategoryCode == "InteriorWallAdjacentToNeighbour" ||
                    category.ConstructionCategoryCode == "InteriorWallAdjacentToRoofSpace" ||
                    category.ConstructionCategoryCode == "InteriorWallAdjacentToSubfloorSpace" ||
                    category.ConstructionCategoryCode == "IntermediateFloorNeighbourBelow")
                {
                    return new List<ConstructionTemplateDto>();
                }
                else
                {
                    List<ConstructionTemplateDto> surfaces = building.Surfaces
                        .Select(x => x as ConstructionTemplateDto)
                        .Where(x => x.Category.ConstructionCategoryCode == category.ConstructionCategoryCode &&
                                    x.ShowInReport == true)
                        .ToList();
                    return surfaces;
                }
            }
            else
            {
                List<ConstructionTemplateDto> openings = building.Openings
                .Select(x => x as ConstructionTemplateDto)
                .Where(x => x.Category.ConstructionCategoryCode == category.ConstructionCategoryCode &&
                            x.ShowInReport == true)
                .ToList();
                return openings;
            }
        }

        public bool ConstructionsGroupedForCategoryHasItems(
            AssessmentComplianceBuildingDto building,
        ConstructionCategoryDto category)
        {
            var group = ConstructionsGroupedForCategory(building, category);

            if (group == null || group.Count == 0)
                return false;

            return true;

        }

        public List<ServiceTemplateDto> ServicesForCategory(
            AssessmentComplianceBuildingDto building,
            ServiceCategoryDto category)
        {
            return building.Services
                .Select(x => x as ServiceTemplateDto)
                .Where(x => x.ServiceCategory.ServiceCategoryCode == category.ServiceCategoryCode &&
                            x.ShowInReport == true)
                .ToList();
        }

        public bool ServiceCategoryHasItems(
            AssessmentComplianceBuildingDto building,
            ServiceCategoryDto category)
        {
            return category.ServiceCategoryCode == "Cooking"
                || building.Services.Where(x => x.ServiceCategory.ServiceCategoryCode == category.ServiceCategoryCode && x.ShowInReport == true).Count() > 0;
        }

        public bool HasVentilation(ConstructionCategoryDto category)
        {
            return category.ConstructionCategoryCode == "Roof" ||
                category.ConstructionCategoryCode == "ExteriorFloor";
        }

        public string RoofSpaceVentilation(SurfaceTemplateDto dto)
        {
            return dto.AirCavity.Title;
        }

        public bool IncludeInsulation(ConstructionCategoryDto category)
        {
            if ((category.Type == "surface" || category.Type == "permanentopening") && category.ConstructionCategoryCode.Contains("Door") == false)
                return true;
            else
                return false;
        }

        public string InsulationDescription(SurfaceTemplateDto construction)
        {
            // Use override insulation description if it exists, otherwise fall back to original
            if (!string.IsNullOrEmpty(construction.OverrideInsulationDescription))
                return construction.OverrideInsulationDescription;
            else if (construction.InsulationData != null && construction.InsulationData.Description != null)
                return construction.InsulationData.Description;
            else
                return "No Insulation";
        }

        // Final Opening Style - Use override if it exists, otherwise fall back to original
        public string FinalOpeningStyle(ConstructionTemplateDto opening)
        {
            return opening.OverrideOpeningStyle?.Title ?? opening.OpeningStyle?.Title;
        }

        // Final U-Value - Use override if it exists, otherwise fall back to original
        public decimal? FinalUValue(OpeningTemplateDto opening)
        {
            return opening.Performance?.OverrideUValue ?? opening.Performance?.UValue;
        }

        // Final SHGC - Use override if it exists, otherwise fall back to original
        public decimal? FinalSHGC(OpeningTemplateDto opening)
        {
            return opening.Performance?.OverrideSHGC ?? opening.Performance?.SHGC;
        }

        public string SolarAbsorptanceType(ConstructionCategoryDto category)
        {
            if (category.Type == "surface" || category.Type == "permanentopening" ||
                category.ConstructionCategoryCode == "ExteriorDoor" ||
                category.ConstructionCategoryCode == "InteriorDoor"
            )
                return "Solar Absorptance";
            else if (category.ConstructionCategoryCode == "VerticalOpening" ||
                category.ConstructionCategoryCode == "HorizontalOpening")
                return "";
            else
                return "Frame Solar Absorptance";
        }

        public string Round2DP(decimal? input)
        {
            if (input != null)
                return string.Format("{0:F2}", input);
            else
                return "";
        }

        public bool isFrame(ConstructionCategoryDto category)
        {
            return (
                category.ConstructionCategoryCode == "ExteriorGlazing" ||
                category.ConstructionCategoryCode == "InteriorGlazing" ||
                category.ConstructionCategoryCode == "Skylight" ||
                category.ConstructionCategoryCode == "RoofWindow"
            );
        }

        public string FormattedSummaryCategoryTitle(string title, string constructionCategoryCode)
        {
            switch (constructionCategoryCode)
            {
                case "ExteriorFloor":
                case "GroundFloor":
                    return "Ground Floor";
                case "ExteriorFloorElevated":
                    return "Exterior Floor";
                case "InteriorWall":
                case "InteriorWallAdjacentToNeighbour":
                case "InteriorWallAdjacentToRoofSpace":
                case "InteriorWallAdjacentToSubfloorSpace":
                    return "Interior Walls";
                case "CeilingNeighbourAbove":
                case "CeilingRoofAbove":
                    return "Ceiling";
                case "IntermediateFloor":
                case "IntermediateFloorNeighbourBelow":
                    return "Intermediate Floor";
                case "ExteriorWall":
                case "SubfloorWall":
                    return title + "s";
                default:
                    return title;
            }
        }

        public string FormattedCategoryTitle(string title, string constructionCategoryCode)
        {
            string formattedTitle;
            switch (constructionCategoryCode)
            {
                case "ExteriorWall":
                case "SubfloorWall":
                case "ExteriorDoor":
                case "InteriorDoor":
                case "VerticalOpening":
                case "HorizontalOpening":
                case "Skylight":
                case "RoofWindow":
                    formattedTitle = title + 's';
                    break;
                case "InteriorWall":
                    formattedTitle = "Interior Walls (Partition)";
                    break;
                case "InteriorWallAdjacentToNeighbour":
                    formattedTitle = "Interior Walls (Adjacent to Neighbour)";
                    break;
                case "InteriorWallAdjacentToRoofSpace":
                    formattedTitle = "Interior Walls (Adjacent to Roof Space)";
                    break;
                case "InteriorWallAdjacentToSubfloorSpace":
                    formattedTitle = "Interior Walls (Adjacent to Subfloor Space)";
                    break;
                default:
                    formattedTitle = title;
                    break;
            }
            return formattedTitle;
        }
        public List<RSS_ZoneType> AllZoneTypes()
        {
            return _unitOfWork.Context.RSS_ZoneType.Where(x => x.Deleted == false).ToList();
        }

        public List<ZoneSummaryGroupDto> InteriorZoneBuildingGroupSummary()
        {
            List<ZoneSummaryGroupDto> zoneGroups = new List<ZoneSummaryGroupDto>();

            var interiorZones = Zones.Where(zone => zone.ZoneActivity == null || (zone.ZoneActivity.ZoneActivityCode != "ZARoofSpace" && zone.ZoneActivity.ZoneActivityCode != "ZASubfloorSpace" && zone.ZoneActivity.ZoneActivityCode != "ZAGroundSurface")).ToList();

            var exteriorWallsElements = _assessmentDto.SelectedSimulation.Proposed.Surfaces?.Where(x => x.Category.ConstructionCategoryCode == "ExteriorWall").Select(x => x.Elements).ToList();
            var exteriorGlazingsElements = _assessmentDto.SelectedSimulation.Proposed.Openings?.Where(x => x.Category.ConstructionCategoryCode == "ExteriorGlazing").Select(x => x.Elements).ToList();
            List<SurfaceTemplateDto> allExtWalls = new List<SurfaceTemplateDto>();
            List<OpeningTemplateDto> allExtGlazings = new List<OpeningTemplateDto>();
            foreach (var list in exteriorWallsElements)
            {
                allExtWalls = allExtWalls.Concat(list).ToList();
            }
            foreach (var list in exteriorGlazingsElements)
            {
                allExtGlazings = allExtGlazings.Concat(list).ToList();
            }
            foreach (var zone in interiorZones)
            {
                List<SurfaceTemplateDto> wallsInZone = allExtWalls.Where(x => x.ParentZoneId == zone.LinkId).ToList();
                List<OpeningTemplateDto> glazingsInZone = allExtGlazings.Where(x => x.ParentZoneId == zone.LinkId).ToList();
                zone.ExteriorWallArea = wallsInZone.Sum(x => x.GrossArea);
                zone.ExteriorGlazingArea = glazingsInZone.Sum(x => x.GrossArea);
            }

            decimal totalFloorArea = interiorZones.Sum(x => x.FloorArea) ?? 0;
            decimal totalVolume = interiorZones.Sum(x => x.Volume) ?? 0;
            decimal totalExteriorWallArea = interiorZones.Sum(x => x.ExteriorWallArea) ?? 0;
            decimal totalGlazingArea = interiorZones.Sum(x => x.ExteriorGlazingArea) ?? 0;
            decimal totalLampPowerMaximumW = interiorZones.Sum(x => x.LampPowerMaximumW) ?? 0;

            foreach (var story in Assessment.AllComplianceOptions[0].Proposed.Storeys)
            {
                ZoneSummaryGroupDto storeyGroup = new ZoneSummaryGroupDto();
                storeyGroup.DescriptionHeading = story.Name;
                storeyGroup.GroupName = story.Name;
                storeyGroup.Zones = interiorZones.Where(zone => zone.Storey == story.Floor).ToList();

                foreach (var zone in storeyGroup.Zones)
                {
                    //zone.FloorAreaPercent = totalFloorArea > 0 ? (zone.FloorArea / totalFloorArea) * 100 : 0;
                    //zone.VolumePercent = totalVolume > 0 ? (zone.Volume / totalVolume) * 100 : 0;
                    //zone.ExteriorWallAreaPercent = totalExteriorWallArea > 0 ? (zone.ExteriorWallArea / totalExteriorWallArea) * 100 : 0;
                    //zone.ExteriorGlazingAreaPercent = totalGlazingArea> 0 ? (zone.ExteriorGlazingArea / totalGlazingArea) * 100 : 0;
                    zone.GlassExteriorWallAreaPercent = zone.ExteriorWallArea > 0 ? (zone.ExteriorGlazingArea / zone.ExteriorWallArea) * 100 : 0;
                    zone.GlassFloorAreaPercent = zone.FloorArea > 0 ? (zone.ExteriorGlazingArea / zone.FloorArea) * 100 : 0;
                    //zone.ZoneCount = 1;
                }

                decimal storyTotalFloorArea = storeyGroup.Zones.Sum(x => x.FloorArea) ?? 0;
                decimal storyTotalVolume = storeyGroup.Zones.Sum(x => x.Volume) ?? 0;
                decimal storyTotalExteriorWallArea = storeyGroup.Zones.Sum(x => x.ExteriorWallArea) ?? 0;
                decimal storyTotalGlazingArea = storeyGroup.Zones.Sum(x => x.ExteriorGlazingArea) ?? 0;
                decimal storyTotalLampPowerMaximumW = storeyGroup.Zones.Sum(x => x.LampPowerMaximumW) ?? 0;

                storeyGroup.Zones = storeyGroup.Zones.OrderBy(x => x.ZoneNumber).ToList();

                ZoneDto totalsData = new ZoneDto();
                totalsData.ZoneNumber = "";
                totalsData.ZoneDescription = "All Zones";
                totalsData.FloorArea = storyTotalFloorArea;
                //totalsData.FloorAreaPercent = totalFloorArea > 0 ? (totalsData.FloorArea / totalFloorArea) * 100 : 0;
                totalsData.Volume = storyTotalVolume;
                //totalsData.VolumePercent = totalVolume > 0 ? (totalsData.Volume / totalVolume) * 100 : 0;
                totalsData.ExteriorWallArea = storyTotalExteriorWallArea;
                //totalsData.ExteriorWallAreaPercent = totalExteriorWallArea > 0 ? (totalsData.ExteriorWallArea / totalExteriorWallArea) * 100 : 0;
                totalsData.ExteriorGlazingArea = storyTotalGlazingArea;
                //totalsData.ExteriorGlazingAreaPercent = totalGlazingArea > 0 ? (totalsData.ExteriorGlazingArea / totalGlazingArea) * 100 : 0;
                totalsData.LampPowerMaximumW = storyTotalLampPowerMaximumW;
                totalsData.GlassExteriorWallAreaPercent = storyTotalExteriorWallArea > 0 ? (storyTotalGlazingArea / storyTotalExteriorWallArea) * 100 : 0;
                totalsData.GlassFloorAreaPercent = storyTotalFloorArea > 0 ? (storyTotalGlazingArea / storyTotalFloorArea) * 100 : 0;
                storeyGroup.Zones.Add(totalsData);

                zoneGroups.Add(storeyGroup);
            }

            // Whole Building
            ZoneSummaryGroupDto wholeBuildingGroup = new ZoneSummaryGroupDto();
            wholeBuildingGroup.DescriptionHeading = "Whole Building";
            wholeBuildingGroup.GroupName = "Whole Building";
            wholeBuildingGroup.Zones = new List<ZoneDto>();
            foreach (string rowName in new List<string> { "habitable", "conditioned", "houseClass1a" })
            {
                ZoneDto wholeBuildingHabitable = new ZoneDto();
                JToken row = Assessment.AllComplianceOptions[0].Proposed.ZoneSummary[rowName];
                if (row != null)
                {
                    wholeBuildingHabitable.ZoneDescription = row["zoneDescription"].ToString();
                    wholeBuildingHabitable.ZoneNumber = "";
                    wholeBuildingHabitable.FloorArea = decimal.Parse(row["floorArea"].ToString());
                    wholeBuildingHabitable.Volume = decimal.Parse(row["volume"].ToString());
                    wholeBuildingHabitable.ExteriorWallArea = decimal.Parse(row["exteriorWallArea"].ToString());
                    wholeBuildingHabitable.ExteriorGlazingArea = decimal.Parse(row["exteriorGlazingArea"].ToString());
                    wholeBuildingHabitable.LampPowerMaximumW = decimal.Parse(row["lampPowerMaximumW"].ToString());
                    wholeBuildingHabitable.GlassExteriorWallAreaPercent = decimal.Parse(row["glassExteriorWallAreaPercent"].ToString());
                    wholeBuildingHabitable.GlassFloorAreaPercent = decimal.Parse(row["glassFloorAreaPercent"].ToString());
                    wholeBuildingGroup.Zones.Add(wholeBuildingHabitable);
                }
            }
            ZoneDto wholeBuildingAllZones = new ZoneDto();
            wholeBuildingAllZones.ZoneDescription = "All Zones";
            wholeBuildingAllZones.ZoneNumber = "";
            wholeBuildingAllZones.FloorArea = totalFloorArea;
            wholeBuildingAllZones.Volume = totalVolume;
            wholeBuildingAllZones.ExteriorWallArea = totalExteriorWallArea;
            wholeBuildingAllZones.ExteriorGlazingArea = totalGlazingArea;
            wholeBuildingAllZones.LampPowerMaximumW = totalLampPowerMaximumW;
            wholeBuildingAllZones.GlassExteriorWallAreaPercent = totalExteriorWallArea > 0 ? (totalGlazingArea / totalExteriorWallArea) * 100 : 0;
            wholeBuildingAllZones.GlassFloorAreaPercent = totalFloorArea > 0 ? (totalGlazingArea / totalFloorArea) * 100 : 0;
            wholeBuildingGroup.Zones.Add(wholeBuildingAllZones);
            zoneGroups.Add(wholeBuildingGroup);

            return zoneGroups;
        }

        public EnvelopeSummaryDto EnvelopeSummary(string section)
        {
            switch (section)
            {
                case "conditioned":
                    return Assessment.AllComplianceOptions[0].Proposed.EnvelopeSummaryConditioned;
                case "habitable":
                    return Assessment.AllComplianceOptions[0].Proposed.EnvelopeSummaryHabitable;
                default:
                    return Assessment.AllComplianceOptions[0].Proposed.EnvelopeSummary;
            }
        }

        public SectorData SectorFromLabel(string label)
        {
            return Assessment.AllComplianceOptions[0].SectorDetermination.Sectors.Find(x => x.Label.ToLower() == label.ToLower());
        }

        public bool IsSectorNotEmpty(string section, string label)
        {
            if (label == "total")
            {
                return true;
            }
            EnvelopeSummaryDto envelopeSummary = EnvelopeSummary(section);
            SectorData sector = SectorFromLabel(label);

            // Check if Exterior Wall Area is greater than 0
            bool isWallNotEmpty = envelopeSummary.ExteriorWallAreaTotalsPerSector.ContainsKey(label) &&
                envelopeSummary.ExteriorWallAreaTotalsPerSector[label]?.Area > 0 &&
                envelopeSummary.ExteriorWallAreaTotalsPerSector[label]?.Percentage > 0;

            // Check if Exterior Glazing Area is greater than 0
            bool isGlazingNotEmpty = envelopeSummary.ExteriorGlazingAreaTotalsPerSector.ContainsKey(label) &&
                envelopeSummary.ExteriorGlazingAreaTotalsPerSector[label]?.Area > 0 &&
                envelopeSummary.ExteriorGlazingAreaTotalsPerSector[label]?.Percentage > 0;

            // A facade should be shown if EITHER the Exterior Wall Area OR Exterior Glazing Area is greater than 0
            return isWallNotEmpty || isGlazingNotEmpty;
        }

        public int GetVisibleColumnCount(string section)
        {
            EnvelopeSummaryDto sectorsData = EnvelopeSummary(section);
            int visibleColumnCount = 1; // Start with 1 for the row heading column

            foreach (string sectorKey in sectorsData.SectorKeys)
            {
                if (IsSectorNotEmpty(section, sectorKey))
                {
                    visibleColumnCount++;
                }
            }

            return visibleColumnCount;
        }

        public bool ShouldUseShortenedSectorLabels(string section)
        {
            // Use shortened labels when there are more than 6 total columns
            // (row heading column + 4 sector columns + Total column = 6)
            return GetVisibleColumnCount(section) > 6;
        }

        public string GetSectorDisplayLabel(string sectorKey, string section)
        {
            if (sectorKey == "total")
            {
                return "Total";
            }

            SectorData sector = SectorFromLabel(sectorKey);
            if (sector == null)
            {
                return sectorKey.ToUpper();
            }

            // Use shortened labels if there are more than 6 columns
            if (ShouldUseShortenedSectorLabels(section))
            {
                return sector.Label.ToUpper();
            }
            else
            {
                return sector.Description;
            }
        }

        public EnvelopeSummarySectorDto GetWallAreaSectorData(string section, string label)
        {
            return EnvelopeSummary(section).ExteriorWallAreaTotalsPerSector[label];
        }

        public EnvelopeSummarySectorDto GetGlazingAreaSectorData(string section, string label)
        {
            return EnvelopeSummary(section).ExteriorGlazingAreaTotalsPerSector[label];
        }

        public decimal? GetWallRatio(string section, string label)
        {
            // If Exterior Wall Area is 0, return null to avoid division by zero
            if (EnvelopeSummary(section).ExteriorWallAreaTotalsPerSector[label]?.Area == 0)
            {
                return null;
            }
            return EnvelopeSummary(section).GlassExteriorWallRatioPerSector[label];
        }

        public decimal? GetAvUValue(string section, string label)
        {
            // Check if Exterior Glazing Area is 0.00, if so return null to display "N/A"
            if (EnvelopeSummary(section).ExteriorGlazingAreaTotalsPerSector[label]?.Area == 0)
            {
                return null;
            }
            return EnvelopeSummary(section).AverageGlazingUValuePerSector[label];
        }

        public decimal? GetAvSHGC(string section, string label)
        {
            // Check if Exterior Glazing Area is 0.00, if so return null to display "N/A"
            if (EnvelopeSummary(section).ExteriorGlazingAreaTotalsPerSector[label]?.Area == 0)
            {
                return null;
            }
            return EnvelopeSummary(section).AverageGlazingSHGCPerSector[label];
        }

        //public List<string> FacadeSummary()
        //{
        //    EnvelopeSummaryDto summary = new EnvelopeSummaryDto();
        //    AssessmentComplianceBuildingDto option = Assessment.AllComplianceOptions[0].Proposed;
        //    summary.SectorKeys = Assessment.AllComplianceOptions[0].SectorDetermination.Sectors.Select(x => x.Label).ToList();
        //    foreach (SurfaceTemplateDto surface in option.Surfaces)
        //    {
        //        foreach (SurfaceTemplateDto element in surface.Elements)
        //        {
        //            List<ZoneDto> windowZones = option.Zones.Where(x => x.LinkId == element.ParentZoneId).ToList();
        //            if (windowZones?.Count == 0) { continue; }
        //            ZoneDto windowZone = windowZones[0];

        //        }
        //    }
        //}

        public decimal SolarAbsorptance(ConstructionTemplateDto dto, ConstructionCategoryDto category)
        {
            if (category.Type == "surface" || category.Type == "permanentopening")
            {
                if (category.ConstructionCategoryCode == "Roof" ||
                    category.ConstructionCategoryCode == "ExteriorWall" ||
                    category.ConstructionCategoryCode == "ExteriorDoor")
                    return ((SurfaceTemplateDto)dto).ExteriorSolarAbsorptance ?? 0;
                else
                    return ((SurfaceTemplateDto)dto).InteriorSolarAbsorptance ?? 0;
            }
            else
                return ((OpeningTemplateDto)dto).FrameSolarAbsorptance ?? 0;
        }

        public bool IsAnOpening(ConstructionCategoryDto category)
        {
            return category.Type != "surface" &&
                   category.ConstructionCategoryCode != "HorizontalOpening" &&
                   category.ConstructionCategoryCode != "VerticalOpening";
        }

        public bool IsARoof(ConstructionCategoryDto category)
        {
            return category.ConstructionCategoryCode == "Roof";
        }

        public bool ServicesArePresent()
        {
            return _assessmentDto.SelectedSimulation.Proposed.Services?.Count > 0;
        }

        public List<AssessmentComplianceBuildingDto> GetBuildings()
        {
            // Proposed and Reference/Deemed-To-Statisy/DTS (They are all the same field)
            var buildingsList = new List<AssessmentComplianceBuildingDto>();
            buildingsList.Add(_assessmentDto.SelectedSimulation.Proposed);
            buildingsList.Add(_assessmentDto.SelectedSimulation.Reference);
            return buildingsList;
        }



        public List<ZoneDto> Zones { get; private set; } = new List<ZoneDto>();
        public List<ZoneDto> ReferenceZones { get; private set; } = new List<ZoneDto>();

        public AssessmentComplianceBuildingDto ProposedBuilding => _assessmentDto.SelectedSimulation.Proposed;
        public AssessmentComplianceBuildingDto ReferenceBuilding => _assessmentDto.SelectedSimulation.Reference;
        public List<ServiceTemplateDto> ReferenceSNV => ReferenceBuilding.Services;

        public string ReferenceBuildingElementsName
            => _assessmentDto.SelectedSimulation.ComplianceMethodCode == "CMPerfSolution" ? "Reference Building Elements" : "Deemed-to-Satisfy Building Elements";

        public bool ServicesNotRequired
        {
            get
            {
                // TODO: This probably needs better logic when we update the PDF...?
                var notRequired = _assessmentDto.SelectedSimulation.Proposed.CategoriesNotRequired;
                foreach (var category in _serviceFactory().GetServiceCategories())
                {
                    if (notRequired[category.ServiceCategoryCode.ToLower()] == false)
                    {
                        // At least 1 category is NOT "not required".
                        return false;
                    }
                }

                return true;
            }
        }

        public string BuildingOrientation
        {
            get
            {
                return _assessmentDto.SelectedSimulation.Proposed.BuildingOrientation.HasValue
                    ? _assessmentDto.SelectedSimulation.Proposed.BuildingOrientation.Value.ToString()
                    : "";
            }
        }

        public string Exposure
        {
            get
            {
                return string.IsNullOrEmpty(_assessmentDto.BuildingExposureDescription) ? "" : _assessmentDto.BuildingExposureDescription;
            }
        }

        private void PopulateAll()
        {
            PopulateAddress();
            PopulateZones();
        }

        private void PopulateAddress()
        {
            if (_assessmentDto.AssessmentProjectDetail == null)
            {
                StreetAddress = new StreetAddress();
                return;
            }

            var lotNum = string.Empty;
            if (_assessmentDto.AssessmentProjectDetail?.LotNumber.HasValue == true)
            {
                lotNum = _assessmentDto.AssessmentProjectDetail?.LotNumber.ToString();
            }
            var streetNum = _assessmentDto.AssessmentProjectDetail?.HouseNumber != null ? _assessmentDto.AssessmentProjectDetail?.HouseNumber : string.Empty;
            var streetName = _assessmentDto.AssessmentProjectDetail?.StreetName != null ? _assessmentDto.AssessmentProjectDetail?.StreetName : string.Empty;
            var streetType = _assessmentDto.AssessmentProjectDetail?.StreetType ?? string.Empty;
            var suburb = _assessmentDto.AssessmentProjectDetail?.Suburb != null ? _assessmentDto.AssessmentProjectDetail?.Suburb : string.Empty;
            var state = _assessmentDto.AssessmentProjectDetail?.StateCode != null ? _assessmentDto.AssessmentProjectDetail?.StateCode : string.Empty;
            var postcode = _assessmentDto.AssessmentProjectDetail?.Postcode != null ? _assessmentDto.AssessmentProjectDetail?.Postcode : string.Empty;
            var lotType = _assessmentDto.AssessmentProjectDetail?.LotType?.Description ?? string.Empty; //planTypeCode
            var prefix = _assessmentDto.AssessmentProjectDetail?.Prefix != null ? _assessmentDto.AssessmentProjectDetail?.Prefix : string.Empty;
            var strataLot = _assessmentDto.AssessmentProjectDetail?.StrataLotNumber != null ? _assessmentDto.AssessmentProjectDetail?.StrataLotNumber.ToString() : string.Empty;
            var surveyStrataLotNumber = _assessmentDto.AssessmentProjectDetail?.SurveyStrataLotNumber != null ? _assessmentDto.AssessmentProjectDetail?.SurveyStrataLotNumber.ToString() : string.Empty;
            var originalLotNumber = _assessmentDto.AssessmentProjectDetail?.OriginalLotNumber != null ? _assessmentDto.AssessmentProjectDetail?.OriginalLotNumber.ToString() : string.Empty;

            StreetAddress = new StreetAddress(lotNum, streetNum, streetName, suburb, state, postcode, lotType, prefix, strataLot, surveyStrataLotNumber, originalLotNumber, streetType);
        }

        // TODO: I think this will have to be updated. I am not sure what the current
        // zone types that need to be added to p1 vs p2 (below) need to be...
        private void PopulateZones()
        {
            List<ZoneDto> p1 = new List<ZoneDto>();
            List<ZoneDto> p2 = new List<ZoneDto>();

            foreach (var zone in _assessmentDto.SelectedSimulation.Proposed.Zones)
            {
                if (zone.ZoneType?.ZoneTypeCode == Zone.ZoneTypeCode.ZTOutdoor.ToString() ||
                    zone.ZoneType?.ZoneTypeCode == Zone.ZoneTypeCode.ZTClass10A.ToString())
                {
                    p1.Add(zone);
                }
                else
                {
                    p2.Add(zone);
                }
            }

            p1.OrderBy(s => s.ZoneType);
            p2.OrderBy(s => s.SortOrder);
            p1.AddRange(p2);

            Zones = p1;
        }

        public string AssessmentNotes()
        {
            return _assessmentDto.AssessorNotes ?? "";
        }

        public bool isEvenNumber(int value)
        {
            return value % 2 == 0;
        }

        public string OneDecimalPlace(decimal? value)
        {
            if (value != null)
            {
                return value.Value.ToString("#.#");
            }
            else
            {
                return "0.0";
            }
        }

        public string addLeadingZero(int number)
        {
            if (number < 10)
            {
                return String.Format("0{0}", number);
            }

            return number.ToString();
        }

        public string PreliminaryComplianceMethodCode
        {
            get
            {
                return _assessmentDto.BaselineSimulation.ComplianceMethodCode;
            }
        }

        public bool ComplianceNotInitiallyAchieved
        {
            get
            {
                // If the first option (baseline run) is NOT the selected simulation,
                // then compliance was not initially achieved...?
                return _assessmentDto.SelectedSimulation.OptionIndex != 0;
            }
        }

        public string ModifySignature(string signature)
        {
            var newSvgSignature = "";
            if (!String.IsNullOrEmpty(signature))
            {
                newSvgSignature = signature;

                //Check for '%' char e.g. height="100%"
                //Check for 'x' char e.g. height = "100px"
                //Check for 'o' char e.g. height = "auto"
                //Check for number values

                //Remove width attribute and value entirely
                var widthStartPos = signature.ToLower().IndexOf("width");
                if (widthStartPos > -1)
                {
                    var wEndPos = signature.ToLower().IndexOfAny(new char[] { '%', 'x', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'o' }, widthStartPos);
                    var wCommaEndPos = signature.IndexOfAny(new char[] { '"', '\'' }, wEndPos);
                    var widthEndPos = (wCommaEndPos != -1 ? wCommaEndPos : wEndPos) - widthStartPos;
                    newSvgSignature = signature.Remove(widthStartPos, widthEndPos);
                }

                //Remove height attribute and value entirely
                var heightStartPos = newSvgSignature.ToLower().IndexOf("height");
                if (heightStartPos > -1)
                {
                    var hEndPos = newSvgSignature.ToLower().IndexOfAny(new char[] { '%', 'x', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'o' }, heightStartPos);
                    var hCommaEndPos = newSvgSignature.IndexOfAny(new char[] { '"', '\'' }, hEndPos);
                    var heightEndPos = (hCommaEndPos != -1 ? hCommaEndPos : hEndPos) - heightStartPos;
                    newSvgSignature = newSvgSignature.Remove(heightStartPos, heightEndPos);
                }

                //Add new values to adjust signature
                //The value '3' is to adjust where the newly 'signature adjust' variables are inserted
                var svgEndPos = signature.IndexOf("svg") + 3;
                newSvgSignature = newSvgSignature.Insert(svgEndPos, " height=\"53px\" preserveAspectRatio=\"xMinYMin meet\"");

            }
            return newSvgSignature;
        }

        public string PBDBProcessDiagramImgLocation
        {
            get
            {
                string directory = HostingEnvironment.MapPath("/Templates/Extra");
                string fullPath = "";

                if (_assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfSolutionDTS.ToString("G"))
                    fullPath = directory + "/PBDBProcessDiagram-DTS-EP.png";
                else if (_assessmentDto.SelectedSimulation.ComplianceMethodCode == ComplianceMethodCode.CMPerfSolutionHER.ToString("G"))
                    fullPath = directory + "/PBDBProcessDiagram-DTS-HER.png";
                else if (_assessmentDto.SelectedSimulation.ComplianceMethodCode ==
                         ComplianceMethodCode.CMPerfSolution.ToString("G"))
                {
                    if(IsNcc2022Certification())
                        fullPath = directory + "/PBDBProcessDiagram-VURB_2022.png";
                    else
                        fullPath = directory + "/PBDBProcessDiagram-VURB_2019.png";
                }


                return fullPath;
            }
        }

        public string StyleSheetLocation
        {
            get
            {
                string directory = HostingEnvironment.MapPath("/Templates");
                return directory + "/template-styles.css";
            }
        }

        public string WAHEREquationImgLocation(int num)
        {

            string directory = HostingEnvironment.MapPath("/Templates/Extra");
            return directory + $"/WA-HER-Equation-0{num}.png";

        }

        public string PerfEllRequirementsImageLocation()
        {
            string directory = HostingEnvironment.MapPath("/Templates/Extra");
            return directory + $"/PBDBRequirements-2022PerfEll.png";
        }

        public string PerfEllExplanationImageLocation()
        {
            string directory = HostingEnvironment.MapPath("/Templates/Extra");
            return directory + $"/PBDBExplanation-2022PerfEll.png";
        }

        // Climate Weather Charts - Heatmap charts are used this way, others are added via InsertSvgsOntoExistingPdf() in GeneratePDF.cs

        public string MonthlyTempChartSVG
        {
            get
            {
                if (_assessmentDto.AssessmentProjectDetail.ClimateChartData.MonthlyTempChartSVG == null)
                    return "";
                else
                    return _assessmentDto.AssessmentProjectDetail.ClimateChartData.MonthlyTempChartSVG;
            }
        }
        public string HeatmapTemperatureChartSVG
        {
            get
            {
                if (_assessmentDto.AssessmentProjectDetail.ClimateChartData.HeatmapTemperatureChartSVG == null)
                    return "";
                else
                {
                    return _assessmentDto.AssessmentProjectDetail.ClimateChartData.HeatmapTemperatureChartSVG;
                }
            }
        }
        public string RadiationColumnChartSVG
        {
            get
            {
                if (_assessmentDto.AssessmentProjectDetail.ClimateChartData.RadiationColumnChartSVG == null)
                    return "";
                else
                    return _assessmentDto.AssessmentProjectDetail.ClimateChartData.RadiationColumnChartSVG;
            }
        }
        public string HeatmapRadiationChartSVG
        {
            get
            {
                if (_assessmentDto.AssessmentProjectDetail.ClimateChartData.HeatmapRadiationChartSVG == null)
                    return "";
                else
                    return _assessmentDto.AssessmentProjectDetail.ClimateChartData.HeatmapRadiationChartSVG;
            }
        }
        public string WindroseChartSVG
        {
            get
            {
                if (_assessmentDto.AssessmentProjectDetail.ClimateChartData.WindroseChartSVG == null)
                    return "";
                else
                    return _assessmentDto.AssessmentProjectDetail.ClimateChartData.WindroseChartSVG;
            }
        }
        public string PrecipitableWaterChartSVG
        {
            get
            {
                if (_assessmentDto.AssessmentProjectDetail.ClimateChartData.PrecipitableWaterChartSVG == null)
                    return "";
                else
                    return _assessmentDto.AssessmentProjectDetail.ClimateChartData.PrecipitableWaterChartSVG;
            }
        }

        public string ThermalPerformanceSummaryChartSVG
        {
            get
            {
                if (_assessmentDto.SelectedSimulation.Proposed.EnergyResultsChartData.ThermalPerformanceSummaryChartSVG == null)
                    return "";
                else
                    return _assessmentDto.SelectedSimulation.Proposed.EnergyResultsChartData.ThermalPerformanceSummaryChartSVG;
            }
        }

        public string BuildingEnergyUseChartSVG => _assessmentDto.SelectedSimulation.Proposed.EnergyResultsChartData?.BuildingEnergyUseChartSVG ?? "";
        public string ZoneEnergyUseChartSVG => _assessmentDto.SelectedSimulation.Proposed.EnergyResultsChartData?.ZoneEnergyUseChartSVG ?? "";
        public string ZoneEnergyUsePerAreaChartSVG => _assessmentDto.SelectedSimulation.Proposed.EnergyResultsChartData?.ZoneEnergyUsePerAreaChartSVG ?? "";

        // TODO: Change based on how many zones are in proposed building.
        public string SimulationResultsChartHeight => HabitableZones().Count <= 17 ? "345px" : "500px";

        public double ZoneTotals(string spaceTypeCode, string measurement)
        {
            measurement = measurement.ToLower();

            double total = 0;

            foreach (var item in this.Zones)
            {
                if (item.ZoneType?.ZoneTypeCode == spaceTypeCode)
                {

                    if (measurement == "floor_area")
                        total += (double)item.FloorArea.Value;

                    if (measurement == "max_lamp_density")
                        total += (double)(item.LampPowerMaximumWM2 ?? 0);

                    if (measurement == "max_allowance")
                        total += (double)(item.LampPowerMaximumW ?? 0);

                }
            }

            return total;
        }

        public bool ZoneTypePresent(string spaceTypeCode)
        {

            foreach (var item in this.Zones)
            {
                if (item.ZoneType?.ZoneTypeCode == spaceTypeCode)
                    return true;
            }

            return false;
        }

        public bool BuildingElementsArePresent()
        {
            var s = _assessmentDto.SelectedSimulation;

            if (s.Proposed.Surfaces.Count > 0 || s.Proposed.Openings.Count > 0)
                return true;

            return false;
        }

        // As Reference building and Deemed-to-Satisy Building are the same thing, this applies to both:
        // (ie. Include Deemed-to-Satisfy Building in Report and Include Reference Building in Report flags)
        public bool IncludeReferenceBuildingElements =>
            _assessmentDto.SelectedSimulation.Reference.IncludeBuildingElementsInReport.HasValue &&
            _assessmentDto.SelectedSimulation.Reference.IncludeBuildingElementsInReport.Value == true;

        public bool HasParcelArea => _assessmentDto.AssessmentProjectDetail.ParcelArea.HasValue;

        public object ParcelArea {

            get
            {
                if (_assessmentDto.AssessmentProjectDetail.ParcelArea.HasValue)
                    return _assessmentDto.AssessmentProjectDetail.ParcelArea.Value;
                else
                    return null;
            }
        }

        public decimal? Latitude => _assessmentDto.AssessmentProjectDetail.Latitude;
        public decimal? Longitude => _assessmentDto.AssessmentProjectDetail.Longitude;

        public string BuildingExposure => _assessmentDto.BuildingExposureDescription;
        public bool? BushfireProne => _assessmentDto.IsBushFireProne;
        public string BushfireAttackLevel => _assessmentDto.BushfireAttackLevel?.Description;
        public decimal? NorthOffset => _assessmentDto.SelectedSimulation.Proposed.BuildingOrientation;

        // Map height needs to change based on whether or not we are able to show the Parcel Area within the above table.
        public int MapHeight => ParcelArea != null ? 480 : 440;

        public bool MapImageExists => !string.IsNullOrEmpty(_assessmentDto?.AssessmentProjectDetail?.MapImageFile?.URL);
        public string MapImageUrl => _assessmentDto.AssessmentProjectDetail.MapImageFile?.URL;
        public bool SiteMapImageExists => !string.IsNullOrEmpty(_assessmentDto?.AssessmentProjectDetail?.SiteMapImageFile?.URL);
        public string SiteMapImageUrl => _assessmentDto.AssessmentProjectDetail.SiteMapImageFile?.URL;
        public bool IncludeMapInPDF => _assessmentDto.IncludeMapInExportedPDF;

        public string WorksDescription
        {
            get
            {
                if (_assessmentDto.WorksDescription.Description == null)
                {
                    return string.Empty;
                }

                return _assessmentDto.WorksDescription.Description;
            }
        }

        public string PoppinsSemiBoldFontLocation
        {
            get
            {
                string path = HostingEnvironment.MapPath("/Templates/Extra/") + "Poppins-SemiBold.ttf";
                return path;
            }
        }

        public string PoppinsRegularFontLocation
        {
            get
            {
                string path = HostingEnvironment.MapPath("/Templates/Extra/") + "Poppins-Regular.ttf";
                return path;
            }
        }

        public string SwirlyGraphicsBackgroundPng
        {
            get
            {
                string path = HostingEnvironment.MapPath("/Templates/Extra/") + "<EMAIL>";
                return path;
            }
        }


        // TODO: This should be split off into a new LightAndVentilation-specific nvelocity data class.
        public List<ZoneDto> HabitableZones()
        {
            return Zones.Where(x => x.ZoneType?.ZoneTypeCode == "ZTHabitableRoom").ToList();
        }

        public string StoreyName(short floor)
        {
            return SelectedSimulation.Proposed.Storeys[floor].Name;
        }

        public string LightOutcomeForZone(ZoneDto zone)
        {
            return zone.NaturalLightAchievedM2 >= zone.NaturalLightRequiredM2
                ? "Pass"
                : "Fail";
        }

        public string VentilationOutcomeForZone(ZoneDto zone)
        {
            return zone.VentilationAchievedM2 >= zone.VentilationRequiredM2
                ? "Pass"
                : "Fail";
        }

        public List<ZoneOpeningScheduleRow> OpeningScheduleForZone(AssessmentComplianceBuildingDto building, ZoneDto zone)
        {
            var rows = new List<ZoneOpeningScheduleRow>();

            var parentGlazing = building.Openings.Where(x => x.Category.ConstructionCategoryCode == "ExteriorGlazing");
            var parentSkylights = building.Openings.Where(x => x.Category.ConstructionCategoryCode == "Skylight" ||
                                                               x.Category.ConstructionCategoryCode == "RoofWindow");

            foreach (var parent in parentGlazing)
            {
                var elementsInZone = parent.Elements.Where(x => x.ParentZoneId == zone.LinkId);
                foreach (var element in elementsInZone)
                {
                    rows.Add(new ZoneOpeningScheduleRow()
                    {
                        OpeningId = element.ElementNumber,
                        OpeningType = parent.Category.Title,
                        OpeningStyle = FinalOpeningStyle(parent),
                        Height = element.Height,
                        Width = element.Width,
                        Area = element.GrossArea,
                        Openability = element.Openability,
                        AchievedLight = element.Width * element.Height,
                        AchievedVentilation = element.NetArea * (element.Openability * 0.01m)
                    });
                }
            }

            // I am leaving this as a separate loop for now, at the moment the only difference is the AchievedLight
            // calculation.
            foreach (var parent in parentSkylights)
            {
                var elementsInZone = parent.Elements.Where(x => x.ParentZoneId == zone.LinkId);
                foreach (var element in elementsInZone)
                {
                    rows.Add(new ZoneOpeningScheduleRow()
                    {
                        OpeningId = element.ElementNumber,
                        OpeningType = parent.Category.Title,
                        OpeningStyle = FinalOpeningStyle(parent),
                        Height = element.Height,
                        Width = element.Width,
                        Area = element.GrossArea,
                        Openability = element.Openability,
                        AchievedLight = element.GrossArea * 3.3333333333m,
                        AchievedVentilation = element.NetArea * (element.Openability * 0.01m)
                    });
                }
            }

            rows.Add(new ZoneOpeningScheduleRow()
            {
                OpeningId = $"{zone.ZoneDescription} Total",
                AchievedLight = zone.NaturalLightAchievedM2,
                AchievedVentilation = zone.VentilationAchievedM2,
                OpeningType = "",
                OpeningStyle = ""
            });

            return rows;
        }

        public class ZoneOpeningScheduleRow
        {
            public dynamic OpeningId { get; set; }
            public dynamic OpeningType { get; set; }
            public dynamic OpeningStyle { get; set; }
            public dynamic Height { get; set; }
            public dynamic Width { get; set; }
            public dynamic Area { get; set; }
            public dynamic Openability { get; set; }
            public dynamic AchievedLight { get; set; }
            public dynamic AchievedVentilation { get; set; }
        }

    }
}