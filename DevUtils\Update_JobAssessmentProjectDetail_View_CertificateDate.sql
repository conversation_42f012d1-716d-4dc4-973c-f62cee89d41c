-- Update RSS_JobAssessmentProjectDetail_View to include CertificateDateOverride
-- This script updates the view to prioritize CertificateDateOverride over CerficateDate
-- when displaying certificate dates on Home Page and Jobs Page

-- Drop the existing view
IF OBJECT_ID('dbo.RSS_JobAssessmentProjectDetail_View', 'V') IS NOT NULL
    DROP VIEW [dbo].[RSS_JobAssessmentProjectDetail_View]
GO

-- Recreate the view with updated certificate date logic
CREATE VIEW [dbo].[RSS_JobAssessmentProjectDetail_View]
AS
SELECT [job].[JobId]                                                                                                  [JobId],
       [job].[CurrentAssessmentId]                                                                                    [CurrentAssessmentId],
       [job].[JobReference]                                                                                           [JobReference],
       [job].[ClientId]                                                                                               [ClientId],
       [client].[ClientName]                                                                                          [ClientName],
       [status].[Description]                                                                                         [JobStatusDescription],
       [job].[CreatedOn]                                                                                              [JobCreatedOn],
       [job].[ModifiedOn]                                                                                             [JobModifiedOn],
       [job].[Deleted]                                                                                                [JobDeleted],
       [assessor].[UserId]                                                                                            [AssessorUserId],
       [assessor].[FullName]                                                                                          [AssessorFullName],
       [assessment].[PriorityCode]                                                                                    [AssessmentPriorityCode],
       [building].[Design]                                                                                            [AssessmentDesign],
       [assessment].[StatusCode]                                                                                      [AssessmentStatusCode],
       [assessment].[CreatedOn]                                                                                       [AssessmentCreatedOn],
       -- Use CertificateDateOverride if available, otherwise fall back to CerficateDate
       -- Convert from UTC to local time (+8 hours for Australian timezone) to avoid timezone display issues
       CASE
           WHEN [assessment].[CertificateDateOverride] IS NOT NULL
           THEN DATEADD(HOUR, 8, [assessment].[CertificateDateOverride])
           WHEN [assessment].[CerficateDate] IS NOT NULL
           THEN DATEADD(HOUR, 8, [assessment].[CerficateDate])
           ELSE NULL
       END                                                                                                         [AssessmentCerficateDate],
       [assessment].[Deleted]                                                                                         [AssessmentDeleted],
       [detail].[ClientJobNumber]                                                                                     [ClientJobNumber],
       [detail].[ProjectOwner]                                                                                        [ProjectOwner],
       [detail].[OrderDate]                                                                                           [OrderDate],
       CASE WHEN [detail].[UseCustomAddress] = 1
            THEN [detail].[CustomDisplayAddress]
            ELSE [detail].[FullAddress] END                                                                           [Address],
       [orderType].[Description]                                                                                      [OrderTypeDescription],
       [creator].[FullName]                                                                                           [CreatorFullName],
       [clientAssignee].[FullName]                                                                                    [ClientAssigneeFullName],
       [building].[ProjectDescriptionCode]                                                                            [ProjectDescriptionCode],
       [projectDescription].[Description]                                                                             [ProjectDescriptionDescription],
       [option].[ComplianceMethodCode]                                                                                [ComplianceMethodCode],
       [complianceMethod].[Description]                                                                               [ComplianceMethodDescription],
       [detail].[AssessmentVersion]                                                                                   [AssessmentVersion],
       [detail].[CreatedOn]                                                                                           [AssessmentProjectDetailCreatedOn],
       [detail].[Deleted]                                                                                             [AssessmentProjectDetailDeleted],
       [priority].[Description]                                                                                       [AssessmentPriorityDescription],
       [assessment].[NatHERSClimateZoneCode]                                                                          [AssessmentNatHERSClimateZone],
       [assessment].[NCCClimateZoneCode]                                                                              [AssessmentNCCClimateZone],
       [building].[NorthOffset]                                                                                       [NorthOffset],
       [option].[Heating]                                                                                             [Heating],
       [option].[Cooling]                                                                                             [Cooling],
       [option].[TotalEnergyLoad]                                                                                     [TotalEnergyLoad],
       [option].[CalculatedHouseEnergyRating]                                                                         [CalculatedHouseEnergyRating],
       [detail].[LotDescription]                                                                                      [AssessmentProjectDetailLotDescription],
       [detail].[LotWidth]                                                                                            [AssessmentProjectDetailLotWidth],
       [detail].[LotLength]                                                                                           [AssessmentProjectDetailLotLength],
       [detail].[ParcelArea]                                                                                          [AssessmentProjectDetailParcelArea],
       [detail].[CornerBlock]                                                                                         [AssessmentProjectDetailCornerBlock],
       [detail].[RearLaneway]                                                                                         [AssessmentProjectDetailRearLaneway],
       [detail].[RuralLot]                                                                                            [AssessmentProjectDetailRuralLot],
       [option].[CertificationId]                                                                                     [CertificationId],
       [certification].[Title]                                                                                        [CertificationTitle],
       [building].[GarageLocation]                                                                                    [GarageLocation],
       [building].[OutdoorLivingLocation]                                                                             [OutdoorLivingLocation],
       [detail].[ComplianceCost]                                                                                      [AssessmentProjectDetailComplianceCost],
       [detail].[Suburb]                                                                                              [AssessmentProjectDetailSuburb],
       [detail].[LocalGovernmentAuthority]                                                                           [AssessmentProjectDetailLGA],
       [detail].[LocalGovernmentAuthorityShort]                                                                      [AssessmentProjectDetailLGAShort],
       [assessment].[NCCClimateZoneCode]                                                                              [AssessmentNCCClimateZone],
       [building].[ProjectDescriptionCode]                                                                            [ProjectDescriptionCode]
FROM [dbo].[RSS_Job] [job]
INNER JOIN [dbo].[RSS_Client] [client] ON [job].[ClientId] = [client].[ClientId]
INNER JOIN [dbo].[RSS_Status] [status] ON [job].[StatusCode] = [status].[StatusCode]
INNER JOIN [dbo].[RSS_Assessment] [assessment] ON [job].[CurrentAssessmentId] = [assessment].[AssessmentId]
INNER JOIN [dbo].[RSS_AssessmentProjectDetail] [detail] ON [assessment].[AssessmentId] = [detail].[AssessmentId]
INNER JOIN [dbo].[RSS_AssessmentComplianceOption] [option] ON [assessment].[AssessmentId] = [option].[AssessmentId] AND [option].[IsBaselineSimulation] = 1
INNER JOIN [dbo].[RSS_AssessmentComplianceBuilding] [building] ON [option].[AssessmentComplianceOptionId] = [building].[AssessmentComplianceOptionId] AND [building].[BuildingType] = 'Proposed'
LEFT JOIN [dbo].[RSS_User] [assessor] ON [assessment].[AssessorUserId] = [assessor].[UserId]
LEFT JOIN [dbo].[RSS_User] [creator] ON [detail].[CreatedByUserId] = [creator].[UserId]
LEFT JOIN [dbo].[RSS_User] [clientAssignee] ON [detail].[ClientAssigneeUserId] = [clientAssignee].[UserId]
LEFT JOIN [dbo].[RSS_ProjectDescription] [projectDescription] ON [building].[ProjectDescriptionCode] = [projectDescription].[ProjectDescriptionCode]
LEFT JOIN [dbo].[RSS_ComplianceMethod] [complianceMethod] ON [option].[ComplianceMethodCode] = [complianceMethod].[ComplianceMethodCode]
LEFT JOIN [dbo].[RSS_Priority] [priority] ON [assessment].[PriorityCode] = [priority].[PriorityCode]
LEFT JOIN [dbo].[RSS_Certification] [certification] ON [option].[CertificationId] = [certification].[CertificationId]
LEFT JOIN [dbo].[RSS_OrderType] [orderType] ON [detail].[OrderTypeCode] = [orderType].[OrderTypeCode]
WHERE [job].[Deleted] = 0 AND [assessment].[Deleted] = 0 AND [detail].[Deleted] = 0
GO

PRINT 'RSS_JobAssessmentProjectDetail_View updated successfully to include CertificateDateOverride logic'
