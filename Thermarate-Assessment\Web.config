﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
  <configSections>
    <sectionGroup name="elmah">
      <section name="security" requirePermission="false" type="Elmah.SecuritySectionHandler, Elmah" />
      <section name="errorLog" requirePermission="false" type="Elmah.ErrorLogSectionHandler, Elmah" />
      <section name="errorMail" requirePermission="false" type="Elmah.ErrorMailSectionHandler, Elmah" />
      <section name="errorFilter" requirePermission="false" type="Elmah.ErrorFilterSectionHandler, Elmah" />
      <section name="errorTweet" requirePermission="false" type="Elmah.ErrorTweetSectionHandler, Elmah" />
    </sectionGroup>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
  </configSections>
  <appSettings>
    <add key="ClientPortalUrl" value="https://thermarate-client-portal.redisstest.com.au" />
    <add key="SessionValidPeriodMins" value="480" />
    <!-- User session is valid for x minutes -->
    <!-- Domain Name for this system -->
    <add key="DomainName" value="RediApp" />
    <add key="TwoFactorIssuer" value="Thermarate" />
    <add key="PasswordResetKey" value="w7wRGzJpvub_kLrVuCYxUJRRRaZxk$RFs3_Ad_yMsWA5$E+!Zg83bY^?D_@HXL2UrT_DTAvsCj^M79hSvHPqd99+wP87BHu6ek^D" />
    <!-- Auth Cookie Domain Name for this system -->
    <add key="AuthDomainName" value="" />
    <add key="CacheData" value="Y" />
    <add key="CurrentEnvironmentName" value="Test" />
    <add key="SMSDefaultFrom" value="Test" />
    <add key="SMSUserName" value="unknown" />
    <add key="SMSPassword" value="unknown" />
    <add key="SMSProviderUrl" value="unknown" />
    <add key="PdfTempDirectory" value="/PDFs/" />
    <add key="PdfTempDraftDirectory" value="/PDFs/Drafts/" />
    <add key="PdfStampDirectory" value="/Templates/Stamps/" />
    <add key="PdfExtraDirectory" value="/Templates/Extra/" />
    <add key="AzureClientId" value="fdbc5ea0-f76c-4679-85d7-180ce6b31cd2" />
    <add key="AzureClientSecret" value="****************************************" />
    <add key="GoogleClientId" value="814856073967-v278dphmt436oqe5pq1bath54et6mhna.apps.googleusercontent.com" />
    <add key="GoogleClientSecret" value="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" />
    <add key="FilesPath" value="/JobFiles/" />
    <add key="AmazonAccessKey" value="********************" />
    <add key="AmazonSecretKey" value="MoAeS2zCD59qj1ufki2amK9GiW7ORIhFjHWBDTl9" />
    <add key="AmazonBucketName" value="redisstest-thermarate" />
    <add key="AmazonS3EndPoint" value="ap-southeast-2" />
    <add key="AmazonRootUrl" value="redisstest-logsys.s3.amazonaws.com" />
    <add key="UploadedFilesFolder" value="UploadFiles" />
    <add key="SiteBaseUrl" value="http://localhost:50520" />
    <add key="SLIP_API_PASSWORD" value="6d0a37f2d16bc779dc3bdf23d0b2fef58c46c677599e5fdaf17ebb46eeadeda636e95641348da8d4b6f0a9fd0bb5e9a2b6b97e4785f5730a8f0a661ff33c3455" />
    <add key="SystemName" value="Thermarate Assessment System" />
    <add key="CompanyName" value="Thermarate" />
    <add key="EmailAddr_Default" value="<EMAIL>" />
    <add key="EmailAddr_NewUser" value="<EMAIL>" />
    <add key="EmailAddr_System" value="<EMAIL>" />
    <add key="GoogleGeocodeUrl" value="https://maps.googleapis.com/maps/api/geocode/json?address=" />
    <add key="GoogleGeocodeKey" value="AIzaSyDKQNu2dNB2FGVUb91J5BKBfqho2kwd1yI" />
    <add key="GoogleApiKey" value="AIzaSyBXtkzqdG9uqZMorp59kw-QEXEOm8bTb_k" />

    <!-- The Test Google Sheet is at https://docs.google.com/spreadsheets/d/1gvoHHhKc5ghOPBvdMi6YJIMzxg3COjfLGceHw5n59Mk/edit?usp=sharing -->
    <add key="GoogleSheetsAppName" value="RediApp" />
    <add key="GoogleSheetsSpreadsheetId" value="1gvoHHhKc5ghOPBvdMi6YJIMzxg3COjfLGceHw5n59Mk" />
    <add key="GoogleSheetsSheetName" value="Sheet1" />
    <add key="GoogleSheetsClientId" value="<EMAIL>" />
    <add key="GoogleSheetsClientEmail" value="<EMAIL>" />
    <add key="GoogleSheetsClientPrivateKey" value="-----BEGIN PRIVATE KEY-----MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDnbDmUcX8ldNKSjaYG0BXUsDUSsln7MPVBc2xULOB7sbqBB4QMxOhObVAOnYctOfJzCxGidAg9eqBURTETL/8nDHka3WXG1CLd4Iud3iLPoz8fS3ZTo/494TOYp+QtV+LBS/fYfw4qPzdkRHfz0OqGq3gBE6DVdbHOi3ceAu2G9sDkkd5+TDpryzoEpFIBH3sbZsvu+yGeIzdYFLFiQEezshO+mWtdSXFlXe+K3fxlCkBiHiJJD4lUB8QeR2FkewNxI1fTZqc+RY7ITkPDWV48pHA6ceWBdxK05Qj1BHW4DblQHK7Ie79z7JhnqSPqB5LrZsYAy8OD32UCn2Ikn4sHAgMBAAECggEAD6v1pqnpWgnnVdmPnaF+Z19UnY+dzIcI7MxAt0Vf7fB5hVH5efxLRD7FCTnwuzoHEA328HRNt8AaPTd/g2GgmuF020IuXMCyT105hzc36BP36woeTFUfZ/7CrDrrMFWylOcTAY9YUB/k0LHBGWFGwiTr5QLFXv88sce71gWNVhjQIgFyj8rQYtNeXknL4dA07QymyIRnpVY1pyugJAreq4uVU2jScalSb+fQDymXWONeSa1vchUK4sZWZ9XjFhrkMRWuhN1Le2wjE+wDtAymP3DGz5jyTYaFmW1IVaZEB0wqp1QWmZ8J1R3hSjML8w2rs7HVC9awP9d9U7IuFwYzaQKBgQD26FkKHbvNrce2BBpHUodPTNuzUzvw3GxZqrkADAYJLbXeXXLhUulrIeLGq426LD8Vm3UjgeG7x0c2/3fSEC8SyQxsM2R8tqvlSzNyN/3XoG8agK1CKUAwcflAgaPx8iofFsRjllz3LNMHSq5qL/SYCCU7KBieNcPH+vVh5bnF2QKBgQDv8eXiX9mtROmKp8YendC1hqNJRWE3bZrAjHsAhmW95BFZy3ZMnZUXoVJViYUIHSVEry0jbDEEhp6CHboN/RaGwkvQnZFUvZnx146P/Y9LvvXmDGIa5gxOm+6De9ywVDb6tCpdi0Kj7c0V4EW7mINttlHLfiXmUHKQyOk6Nd7r3wKBgHX6UcleqcU+u0XCshfQXG1M6VHHfMW7XV5/EATwudWa7OMCvgjiiDwzpduOT5tc1wvpC1DbF9SVBPdHnKZYK1YLkQ8Gg4Z/9qUKiNuT24wz0cLVJ3SMp2o+m33yNDdAolXWmCKJz4Qf+/no+mws2+/lPH0GzLSUNieRR7zuexUJAoGAFxKtxmztI6pCwuCLPppyXWJLBVq0lH3nqahYk55Y3j+2I2pbP9bPZWxP3IXS0wrqcIZf0s4ukH7Zcy2knH+/0MmSPMdKGFn7GbResx3wz5nXT33VWTzR5iU8dExO1m3is4zrnf8sM1NEq1vbmMR7rusfRnAk+Na3jZCkTMKtYwUCgYEAwyQBhIWpIEswQt8+jZDjgsnX8qcaY0ZkHW3cDZlL2m+y8qPx47cwY9Y1F+bf81ei+JlA4YVbM9+YqduJsEbH4zkc76/7UaxiAg6JPoVIOIkdk1QrJlHty1axHfG/Fk9H3nfALrBKi3nDnol0hWi3s6pCgZJw+P1Fou+4vcQAkmY=-----END PRIVATE KEY-----" />
    <add key="ApplicationId" value="D8C8B73E-45BB-4C48-90BA-DD37DD5C3A4B" />
    <add key="PSMAConsumerKey" value="oG9ADDGRiMV8VtxBb33HHkdUUu6gxv85" />
    <!-- Comms -->
    <add key="UseSsl" value="true" />
    <add key="DefaultToEmail" value="<EMAIL>" />
    <add key="DefaultFromEmail" value="<EMAIL>" />
    <!-- NOTE: This is currently an api token for the "door knockers test" account, as it is a sandbox and we have
         no dedicated Thermarate test server... -->
    <!--<add key="PostmarkServerApiToken" value= "NONE"/>-->
    <add key="PostmarkServerApiToken" value="************************************" />
    <add key="ClientSiteBaseUrl" value="http://thermarate-client-portal-3.redisstest.com.au" />
    <add key="AdminSiteBaseUrl" value="http://thermarate-assessment-3.redisstest.com.au" />
    <!-- -->
    <add key="EPPlus:ExcelPackage.LicenseContext" value="Commercial" />
  </appSettings>
  <connectionStrings>
    <!-- Local SQL DB-->
    <!--<add name="RediSoftware" connectionString="Data Source=localhost;Initial Catalog=thermarate-live-test-backup;Integrated Security=True" providerName="System.Data.SqlClient" /><add name="Entities" connectionString="metadata=res://*/Models.Entities.csdl|res://*/Models.Entities.ssdl|res://*/Models.Entities.msl;provider=System.Data.SqlClient;provider connection string=&quot;Data Source=localhost;Initial Catalog=thermarate-live-test-backup;Integrated Security=True;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
    <!-- Test 2 Server SQL DB -->
    <add name="RediSoftware" connectionString="Data Source=test2.redisoftware.com.au;Initial Catalog=thermarate; Integrated Security=False;user=sa;password=**************$45mKP*910aPF1wMsLrBDFeal" providerName="System.Data.SqlClient" /><add name="Entities" connectionString="metadata=res://*/Models.Entities.csdl|res://*/Models.Entities.ssdl|res://*/Models.Entities.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=test2.redisoftware.com.au;initial catalog=thermarate;integrated security=False;user=sa;password=**************$45mKP*910aPF1wMsLrBDFeal;multipleactiveresultsets=True;application name=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <!-- Live Test Backup SQL DB -->
    <!--<add name="RediSoftware" connectionString="Data Source=tcp:test-thermarate-db.database.windows.net,1433;Initial Catalog=test-thermarate2; Integrated Security=False;user=test_user;password=****$hdhHA$5ghdjJgdftyj$6" providerName="System.Data.SqlClient" /><add name="Entities" connectionString="metadata=res://*/Models.Entities.csdl|res://*/Models.Entities.ssdl|res://*/Models.Entities.msl;provider=System.Data.SqlClient;provider connection string=&quot;Data Source=tcp:test-thermarate-db.database.windows.net,1433;Initial Catalog=test-thermarate2;Integrated Security=False;User=test_user;Password=****$hdhHA$5ghdjJgdftyj$6;MultipleActiveResultSets=True;Application Name=EntityFramework;&quot;" providerName="System.Data.EntityClient" />-->
    <!-- Demo DB -->
    <!--<add name="RediSoftware" connectionString="Data Source=demo-thermarate-db.database.windows.net;Initial Catalog=demo-thermarate;Integrated Security=False;user=sqladmin;password=******$FewqRAAs&amp;6SoXt06MvI;Min Pool Size=5;Max Pool Size=100;Pooling=True;multipleactiveresultsets=True;application name=ThermarateClientPortal" providerName="System.Data.SqlClient" /><add name="Entities" connectionString="metadata=res://*/;provider=System.Data.SqlClient;provider connection string=&quot;Data Source=demo-thermarate-db.database.windows.net;Initial Catalog=demo-thermarate;Integrated Security=False;user=sqladmin;password=******$FewqRAAs&amp;6SoXt06MvI;Min Pool Size=5;Max Pool Size=100;Pooling=True;multipleactiveresultsets=True;application name=ThermarateClientPortal&quot;" providerName="System.Data.EntityClient" />-->
  </connectionStrings>
  <system.net>
    <mailSettings>
      <smtp from="<EMAIL>">
        <network host="smtp.office365.com" port="587" userName="<EMAIL>" password="G27.p91$ab" enableSsl="true" />
      </smtp>
      <!--<smtp deliveryMethod="Network" from="<EMAIL>">
        <network host="mail.redisoftsol.com.au" port="25" userName="<EMAIL>" password="P21$x#91a7!" />
      </smtp>-->
    </mailSettings>
  </system.net>
  <elmah>
    <!--
        See http://code.google.com/p/elmah/wiki/SecuringErrorLogPages for
        more information on remote access and securing ELMAH.
    -->
    <security allowRemoteAccess="1" />
    <errorLog type="Elmah.SqlErrorLog, Elmah" connectionStringName="RediSoftware" />
  </elmah>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.6" />
      </system.Web>
  -->
  <system.web>
    <authentication mode="Forms">
      <forms timeout="480" name="RediAuthCookie" protection="All" slidingExpiration="true" />
    </authentication>
    <compilation debug="true" targetFramework="4.6.1">
      <assemblies>
        <add assembly="System.Data.Entity, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
      </assemblies>
    </compilation>
    <httpRuntime targetFramework="4.5" maxRequestLength="1048576" executionTimeout="1800" />
    <httpModules>
      <add name="ErrorLog" type="Elmah.ErrorLogModule, Elmah" />
      <add name="ErrorMail" type="Elmah.ErrorMailModule, Elmah" />
      <add name="ErrorFilter" type="Elmah.ErrorFilterModule, Elmah" />
    </httpModules>
  </system.web>
  <system.webServer>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="1073741824" />
      </requestFiltering>
    </security>
    <handlers>
      <remove name="WebDAV" />
      <add name="ElmahHandler" verb="POST,GET,HEAD" path="elmah.axd" type="Elmah.ErrorLogPageFactory, Elmah" />
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
    <modules runAllManagedModulesForAllRequests="true">
      <remove name="WebDAVModule" />
      <add name="ErrorLog" type="Elmah.ErrorLogModule, Elmah" preCondition="managedHandler" />
      <add name="ErrorMail" type="Elmah.ErrorMailModule, Elmah" preCondition="managedHandler" />
      <add name="ErrorFilter" type="Elmah.ErrorFilterModule, Elmah" preCondition="managedHandler" />
    </modules>
    <validation validateIntegratedModeConfiguration="false" />
    <!-- Comment out the following HTTPS/SSL re-direct rules to debug locally. -->
    <rewrite>
      <rules>
        <rule name="Redirect HTTP to HTTPS" stopProcessing="true">
          <match url="^(.*)$" />
          <conditions>
            <add input="{HTTPS}" pattern="^OFF$" />
            <add input="{HTTP_HOST}" matchType="Pattern" pattern="^localhost$" negate="true" />
            <add input="{HTTP_HOST}" matchType="Pattern" pattern="localhost" negate="true" />
            <!-- Skip redirect for Demo site -->
            <add input="{HTTP_HOST}" pattern="^thermarate-assessment-demo\.prelive\.dev$" negate="true" />
          </conditions>
          <action type="Redirect" url="https://{HTTP_HOST}/{R:1}" redirectType="SeeOther" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="v11.0" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <location path="elmah.axd" inheritInChildApplications="false">
    <system.web>
      <httpHandlers>
        <add verb="POST,GET,HEAD" path="elmah.axd" type="Elmah.ErrorLogPageFactory, Elmah" />
      </httpHandlers>
      <!--
        See http://code.google.com/p/elmah/wiki/SecuringErrorLogPages for
        more information on using ASP.NET authorization securing ELMAH.

      <authorization>
        <allow roles="admin" />
        <deny users="*" />
      </authorization>
      -->
    </system.web>
    <system.webServer>
      <handlers>
        <add name="ELMAH" verb="POST,GET,HEAD" path="elmah.axd" type="Elmah.ErrorLogPageFactory, Elmah" preCondition="integratedMode" />
      </handlers>
    </system.webServer>
  </location>
  <!-- Never cache the index.html file to ensure users always pick up the latest. -->
  <location path="index.html">
    <system.webServer>
      <httpProtocol>
        <customHeaders>
          <add name="Cache-Control" value="no-cache" />
        </customHeaders>
      </httpProtocol>
    </system.webServer>
  </location>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Xml.XPath.XDocument" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Xml.XmlSerializer" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Xml.XDocument" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Xml.ReaderWriter" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.1.0" newVersion="4.1.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Timer" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Parallel" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Overlapped" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.RegularExpressions" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.1.0" newVersion="4.1.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Encoding.Extensions" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Encoding" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.SecureString" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.Principal" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.Cryptography.Algorithms" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.Serialization.Xml" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.Serialization.Primitives" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.Serialization.Json" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.Numerics" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.InteropServices.RuntimeInformation" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.2.0" newVersion="4.0.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.InteropServices" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.2.0" newVersion="4.1.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.Extensions" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.2.0" newVersion="4.1.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.2.0" newVersion="4.1.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Resources.ResourceManager" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Reflection.Primitives" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Reflection.Extensions" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Reflection" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.2.0" newVersion="4.1.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ObjectModel" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Sockets" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Requests" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Primitives" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.NetworkInformation" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.2.0" newVersion="4.1.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Linq.Queryable" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Linq.Parallel" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Linq.Expressions" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.2.0" newVersion="4.1.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Linq" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.2.0" newVersion="4.1.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.IO" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.2.0" newVersion="4.1.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.IO.Compression" publicKeyToken="B77A5C561934E089" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Globalization.Extensions" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Globalization" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Dynamic.Runtime" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.Tracing" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.Tools" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.StackTrace" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.Debug" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Data.Common" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ComponentModel.EventBasedAsync" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ComponentModel" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Collections" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Collections.Concurrent" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ValueTuple" publicKeyToken="CC7B13FFCD2DDD51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.Contracts" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ComponentModel.Annotations" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.1.0" newVersion="4.2.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.SqlServer.Types" publicKeyToken="89845dcd8080cc91" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Google.Apis" publicKeyToken="4b01fa6e34db77ab" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.68.0.0" newVersion="1.68.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Google.Apis.Core" publicKeyToken="4b01fa6e34db77ab" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.68.0.0" newVersion="1.68.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.0.0" newVersion="3.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.2.0.0" newVersion="2.2.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Logging" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.2.0.0" newVersion="2.2.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.AspNetCore.WebUtilities" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.2.0.0" newVersion="2.2.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.0.0" newVersion="3.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Configuration" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.0.0" newVersion="3.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="GeoAPI" publicKeyToken="a1a0da7def465678" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.7.5.0" newVersion="1.7.5.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="NetTopologySuite" publicKeyToken="f580a05016ebada1" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="2.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="SQLitePCLRaw.core" publicKeyToken="1488e028ca7ab535" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.6.1341" newVersion="2.0.6.1341" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="SQLitePCLRaw.provider.dynamic_cdecl" publicKeyToken="b68184102cba0b3b" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.6.1341" newVersion="2.0.6.1341" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.0.0.1" newVersion="5.0.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.4.0" newVersion="4.1.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.2.0.0" newVersion="2.2.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.2.0.0" newVersion="2.2.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Net.Http.Headers" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.2.0.0" newVersion="2.2.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.2.0" newVersion="4.2.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.2.0" newVersion="4.2.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.Cookies" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.2.0" newVersion="4.2.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.1" newVersion="6.0.0.1" />
      </dependentAssembly>
    </assemblyBinding>
    <gcAllowVeryLargeObjects enabled="true" />
  </runtime>
  <system.serviceModel>
    <bindings />
  </system.serviceModel>
</configuration>